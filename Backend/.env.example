# Application Configuration
NODE_ENV=development
PORT=3001
APP_NAME=PHCityRent API
APP_VERSION=1.0.0
APP_DESCRIPTION=Enterprise-grade API for Port Harcourt Real Estate Platform
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
API_PREFIX=api/v1

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=phcityrent_user
DB_PASSWORD=your_secure_password_here
DB_NAME=phcityrent
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_QUEUE_DB=1
REDIS_SESSION_DB=2

# Authentication Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production-min-32-chars
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=12
PASSWORD_RESET_EXPIRY=3600000
EMAIL_VERIFICATION_EXPIRY=86400000
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp
UPLOAD_PATH=./uploads

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Payment Gateway Configuration
# Paystack
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key
PAYSTACK_WEBHOOK_SECRET=your_paystack_webhook_secret

# Flutterwave
FLUTTERWAVE_SECRET_KEY=FLWSECK_TEST-your_flutterwave_secret_key
FLUTTERWAVE_PUBLIC_KEY=FLWPUBK_TEST-your_flutterwave_public_key
FLUTTERWAVE_WEBHOOK_SECRET=your_flutterwave_webhook_secret

# Rate Limiting Configuration
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Caching Configuration
CACHE_TTL=300
CACHE_MAX_ITEMS=1000

# Pagination Configuration
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Monitoring Configuration
ENABLE_METRICS=false
METRICS_PORT=9090
