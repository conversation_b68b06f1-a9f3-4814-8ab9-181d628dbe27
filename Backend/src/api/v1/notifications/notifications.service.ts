import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { 
  Notification, 
  NotificationType, 
  NotificationChannel, 
  NotificationStatus 
} from './entities/notification.entity';
import { User, UserRole } from '../../../modules/users/entities/user.entity';
import { PaginationUtil, PaginationResult } from '../../../common/utils/pagination.util';
import { PaginationDto } from '../../../common/dto/pagination.dto';

export interface CreateNotificationDto {
  type: NotificationType;
  channel: NotificationChannel;
  title: string;
  message: string;
  userId: string;
  recipientEmail?: string;
  recipientPhone?: string;
  pushToken?: string;
  data?: Record<string, any>;
  actionUrl?: string;
}

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly configService: ConfigService,
  ) {}

  async create(createNotificationDto: CreateNotificationDto): Promise<Notification> {
    const notification = this.notificationRepository.create(createNotificationDto);
    const savedNotification = await this.notificationRepository.save(notification);

    // Queue notification for sending (in a real app, this would use a queue system)
    this.queueNotification(savedNotification);

    return savedNotification;
  }

  async findAll(paginationDto: PaginationDto, user?: User): Promise<PaginationResult<Notification>> {
    const queryBuilder = this.notificationRepository
      .createQueryBuilder('notification')
      .leftJoinAndSelect('notification.user', 'user');

    // Apply user-based filtering
    if (user && user.role !== UserRole.ADMIN) {
      queryBuilder.andWhere('notification.userId = :userId', { userId: user.id });
    }

    if (paginationDto.search) {
      queryBuilder.andWhere(
        '(notification.title ILIKE :search OR notification.message ILIKE :search)',
        { search: `%${paginationDto.search}%` }
      );
    }

    return PaginationUtil.paginate(queryBuilder, {
      page: paginationDto.page || 1,
      limit: paginationDto.limit || 20,
      sortBy: paginationDto.sortBy || 'createdAt',
      sortOrder: paginationDto.sortOrder || 'DESC',
    });
  }

  async findOne(id: string, user?: User): Promise<Notification> {
    const queryBuilder = this.notificationRepository
      .createQueryBuilder('notification')
      .leftJoinAndSelect('notification.user', 'user')
      .where('notification.id = :id', { id });

    // Apply user-based filtering
    if (user && user.role !== UserRole.ADMIN) {
      queryBuilder.andWhere('notification.userId = :userId', { userId: user.id });
    }

    const notification = await queryBuilder.getOne();

    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }

    return notification;
  }

  async markAsRead(id: string, user: User): Promise<Notification> {
    const notification = await this.findOne(id, user);

    notification.status = NotificationStatus.READ;
    notification.readAt = new Date();

    return this.notificationRepository.save(notification);
  }

  async markAllAsRead(userId: string): Promise<void> {
    await this.notificationRepository.update(
      { userId, status: NotificationStatus.DELIVERED },
      { status: NotificationStatus.READ, readAt: new Date() }
    );
  }

  async getUnreadCount(userId: string): Promise<number> {
    return this.notificationRepository.count({
      where: {
        userId,
        status: NotificationStatus.DELIVERED,
      },
    });
  }

  async getUserNotifications(userId: string, paginationDto: PaginationDto): Promise<PaginationResult<Notification>> {
    const queryBuilder = this.notificationRepository
      .createQueryBuilder('notification')
      .where('notification.userId = :userId', { userId });

    if (paginationDto.search) {
      queryBuilder.andWhere(
        '(notification.title ILIKE :search OR notification.message ILIKE :search)',
        { search: `%${paginationDto.search}%` }
      );
    }

    return PaginationUtil.paginate(queryBuilder, {
      page: paginationDto.page || 1,
      limit: paginationDto.limit || 20,
      sortBy: paginationDto.sortBy || 'createdAt',
      sortOrder: paginationDto.sortOrder || 'DESC',
    });
  }

  async getNotificationsByType(type: NotificationType, user?: User): Promise<Notification[]> {
    const queryBuilder = this.notificationRepository
      .createQueryBuilder('notification')
      .leftJoinAndSelect('notification.user', 'user')
      .where('notification.type = :type', { type });

    // Apply user-based filtering
    if (user && user.role !== UserRole.ADMIN) {
      queryBuilder.andWhere('notification.userId = :userId', { userId: user.id });
    }

    return queryBuilder.getMany();
  }

  async getNotificationStats(user?: User): Promise<any> {
    let queryBuilder = this.notificationRepository.createQueryBuilder('notification');

    // Apply user-based filtering
    if (user && user.role !== UserRole.ADMIN) {
      queryBuilder = queryBuilder.where('notification.userId = :userId', { userId: user.id });
    }

    const [
      totalNotifications,
      sentNotifications,
      deliveredNotifications,
      readNotifications,
      failedNotifications,
    ] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('notification.status = :status', { status: NotificationStatus.SENT }).getCount(),
      queryBuilder.clone().andWhere('notification.status = :status', { status: NotificationStatus.DELIVERED }).getCount(),
      queryBuilder.clone().andWhere('notification.status = :status', { status: NotificationStatus.READ }).getCount(),
      queryBuilder.clone().andWhere('notification.status = :status', { status: NotificationStatus.FAILED }).getCount(),
    ]);

    return {
      totalNotifications,
      sentNotifications,
      deliveredNotifications,
      readNotifications,
      failedNotifications,
      deliveryRate: totalNotifications > 0 ? (deliveredNotifications / totalNotifications) * 100 : 0,
      readRate: deliveredNotifications > 0 ? (readNotifications / deliveredNotifications) * 100 : 0,
    };
  }

  async sendPropertyInquiry(propertyId: string, landlordId: string, inquirerName: string): Promise<void> {
    const landlord = await this.userRepository.findOne({ where: { id: landlordId } });
    if (!landlord) return;

    await this.create({
      type: NotificationType.PROPERTY_INQUIRY,
      channel: NotificationChannel.EMAIL,
      title: 'New Property Inquiry',
      message: `You have received a new inquiry from ${inquirerName} for your property listing.`,
      userId: landlordId,
      recipientEmail: landlord.email,
      data: { propertyId, inquirerName },
      actionUrl: `/properties/${propertyId}`,
    });

    // Also send in-app notification
    await this.create({
      type: NotificationType.PROPERTY_INQUIRY,
      channel: NotificationChannel.IN_APP,
      title: 'New Property Inquiry',
      message: `You have received a new inquiry from ${inquirerName} for your property listing.`,
      userId: landlordId,
      data: { propertyId, inquirerName },
      actionUrl: `/properties/${propertyId}`,
    });
  }

  async sendPaymentReceived(paymentId: string, recipientId: string, amount: number): Promise<void> {
    const recipient = await this.userRepository.findOne({ where: { id: recipientId } });
    if (!recipient) return;

    await this.create({
      type: NotificationType.PAYMENT_RECEIVED,
      channel: NotificationChannel.EMAIL,
      title: 'Payment Received',
      message: `You have received a payment of ₦${amount.toLocaleString()}.`,
      userId: recipientId,
      recipientEmail: recipient.email,
      data: { paymentId, amount },
      actionUrl: `/payments/${paymentId}`,
    });
  }

  async sendPaymentDue(userId: string, amount: number, dueDate: Date): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) return;

    await this.create({
      type: NotificationType.PAYMENT_DUE,
      channel: NotificationChannel.EMAIL,
      title: 'Payment Due Reminder',
      message: `Your payment of ₦${amount.toLocaleString()} is due on ${dueDate.toLocaleDateString()}.`,
      userId,
      recipientEmail: user.email,
      data: { amount, dueDate },
      actionUrl: '/payments',
    });

    // Also send SMS if phone is available
    if (user.phone) {
      await this.create({
        type: NotificationType.PAYMENT_DUE,
        channel: NotificationChannel.SMS,
        title: 'Payment Due Reminder',
        message: `Your payment of ₦${amount.toLocaleString()} is due on ${dueDate.toLocaleDateString()}.`,
        userId,
        recipientPhone: user.phone,
        data: { amount, dueDate },
      });
    }
  }

  async sendPropertyApproved(propertyId: string, landlordId: string): Promise<void> {
    const landlord = await this.userRepository.findOne({ where: { id: landlordId } });
    if (!landlord) return;

    await this.create({
      type: NotificationType.PROPERTY_APPROVED,
      channel: NotificationChannel.EMAIL,
      title: 'Property Approved',
      message: 'Your property listing has been approved and is now live on the platform.',
      userId: landlordId,
      recipientEmail: landlord.email,
      data: { propertyId },
      actionUrl: `/properties/${propertyId}`,
    });
  }

  async sendWelcomeNotification(userId: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) return;

    await this.create({
      type: NotificationType.USER_REGISTERED,
      channel: NotificationChannel.EMAIL,
      title: 'Welcome to PHCityRent!',
      message: `Welcome ${user.firstName}! Your account has been created successfully. Start exploring properties in Port Harcourt.`,
      userId,
      recipientEmail: user.email,
      data: { userRole: user.role },
      actionUrl: '/dashboard',
    });
  }

  async retryFailedNotifications(): Promise<void> {
    const failedNotifications = await this.notificationRepository.find({
      where: { status: NotificationStatus.FAILED, retryCount: { lt: 3 } as any },
      take: 100,
    });

    for (const notification of failedNotifications) {
      notification.retryCount += 1;
      notification.status = NotificationStatus.PENDING;
      await this.notificationRepository.save(notification);
      this.queueNotification(notification);
    }
  }

  private async queueNotification(notification: Notification): Promise<void> {
    // In a real application, this would queue the notification for processing
    // For now, we'll simulate sending the notification
    try {
      await this.simulateSendNotification(notification);
      
      notification.status = NotificationStatus.DELIVERED;
      notification.sentAt = new Date();
      notification.deliveredAt = new Date();
      
      await this.notificationRepository.save(notification);
    } catch (error) {
      notification.status = NotificationStatus.FAILED;
      notification.errorMessage = error.message;
      
      await this.notificationRepository.save(notification);
    }
  }

  private async simulateSendNotification(notification: Notification): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));

    // Simulate occasional failures (5% failure rate)
    if (Math.random() < 0.05) {
      throw new Error('Simulated network error');
    }

    console.log(`📧 Notification sent: ${notification.title} to ${notification.recipientEmail || notification.recipientPhone || 'in-app'}`);
  }
}
