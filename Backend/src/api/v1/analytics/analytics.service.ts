import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserRole } from '../../../modules/users/entities/user.entity';
import { Property, PropertyStatus, PropertyType } from '../properties/entities/property.entity';
import { Payment, PaymentStatus, PaymentType } from '../payments/entities/payment.entity';

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
  ) {}

  async getDashboardStats(): Promise<any> {
    const [
      totalUsers,
      totalProperties,
      totalPayments,
      activeAgents,
      availableProperties,
      rentedProperties,
      totalRevenue,
      monthlyRevenue,
    ] = await Promise.all([
      this.userRepository.count(),
      this.propertyRepository.count(),
      this.paymentRepository.count(),
      this.userRepository.count({ where: { role: UserRole.AGENT, isActive: true } }),
      this.propertyRepository.count({ where: { status: PropertyStatus.AVAILABLE } }),
      this.propertyRepository.count({ where: { status: PropertyStatus.RENTED } }),
      this.getTotalRevenue(),
      this.getMonthlyRevenue(),
    ]);

    return {
      users: {
        total: totalUsers,
        activeAgents,
      },
      properties: {
        total: totalProperties,
        available: availableProperties,
        rented: rentedProperties,
        occupancyRate: totalProperties > 0 ? (rentedProperties / totalProperties) * 100 : 0,
      },
      payments: {
        total: totalPayments,
        totalRevenue,
        monthlyRevenue,
      },
    };
  }

  async getPropertyAnalytics(): Promise<any> {
    const [
      propertyTypeDistribution,
      priceRangeDistribution,
      locationDistribution,
      statusDistribution,
      monthlyListings,
    ] = await Promise.all([
      this.getPropertyTypeDistribution(),
      this.getPriceRangeDistribution(),
      this.getLocationDistribution(),
      this.getPropertyStatusDistribution(),
      this.getMonthlyListings(),
    ]);

    return {
      typeDistribution: propertyTypeDistribution,
      priceRangeDistribution,
      locationDistribution,
      statusDistribution,
      monthlyListings,
    };
  }

  async getUserAnalytics(): Promise<any> {
    const [
      userRoleDistribution,
      monthlyRegistrations,
      userActivityStats,
      topLandlords,
      topAgents,
    ] = await Promise.all([
      this.getUserRoleDistribution(),
      this.getMonthlyRegistrations(),
      this.getUserActivityStats(),
      this.getTopLandlords(),
      this.getTopAgents(),
    ]);

    return {
      roleDistribution: userRoleDistribution,
      monthlyRegistrations,
      activityStats: userActivityStats,
      topLandlords,
      topAgents,
    };
  }

  async getPaymentAnalytics(): Promise<any> {
    const [
      paymentTypeDistribution,
      paymentStatusDistribution,
      monthlyPayments,
      revenueByType,
      paymentTrends,
    ] = await Promise.all([
      this.getPaymentTypeDistribution(),
      this.getPaymentStatusDistribution(),
      this.getMonthlyPayments(),
      this.getRevenueByType(),
      this.getPaymentTrends(),
    ]);

    return {
      typeDistribution: paymentTypeDistribution,
      statusDistribution: paymentStatusDistribution,
      monthlyPayments,
      revenueByType,
      trends: paymentTrends,
    };
  }

  async getMarketInsights(): Promise<any> {
    const [
      averagePrices,
      pricetrends,
      demandAnalysis,
      popularAmenities,
      marketActivity,
    ] = await Promise.all([
      this.getAveragePrices(),
      this.getPriceTrends(),
      this.getDemandAnalysis(),
      this.getPopularAmenities(),
      this.getMarketActivity(),
    ]);

    return {
      averagePrices,
      pricetrends,
      demandAnalysis,
      popularAmenities,
      marketActivity,
    };
  }

  private async getTotalRevenue(): Promise<number> {
    const result = await this.paymentRepository
      .createQueryBuilder('payment')
      .select('SUM(payment.amount)', 'total')
      .where('payment.status = :status', { status: PaymentStatus.COMPLETED })
      .getRawOne();

    return parseFloat(result.total) || 0;
  }

  private async getMonthlyRevenue(): Promise<number> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const result = await this.paymentRepository
      .createQueryBuilder('payment')
      .select('SUM(payment.amount)', 'total')
      .where('payment.status = :status', { status: PaymentStatus.COMPLETED })
      .andWhere('payment.createdAt >= :startOfMonth', { startOfMonth })
      .getRawOne();

    return parseFloat(result.total) || 0;
  }

  private async getPropertyTypeDistribution(): Promise<any[]> {
    return this.propertyRepository
      .createQueryBuilder('property')
      .select('property.propertyType', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('property.propertyType')
      .orderBy('count', 'DESC')
      .getRawMany();
  }

  private async getPriceRangeDistribution(): Promise<any[]> {
    const ranges = [
      { min: 0, max: 500000, label: 'Under ₦500K' },
      { min: 500000, max: 1000000, label: '₦500K - ₦1M' },
      { min: 1000000, max: 2000000, label: '₦1M - ₦2M' },
      { min: 2000000, max: 5000000, label: '₦2M - ₦5M' },
      { min: 5000000, max: 999999999, label: 'Above ₦5M' },
    ];

    const distribution = [];
    for (const range of ranges) {
      const count = await this.propertyRepository
        .createQueryBuilder('property')
        .where('property.pricePerYear >= :min AND property.pricePerYear < :max', {
          min: range.min,
          max: range.max,
        })
        .getCount();

      distribution.push({
        label: range.label,
        count,
      });
    }

    return distribution;
  }

  private async getLocationDistribution(): Promise<any[]> {
    return this.propertyRepository
      .createQueryBuilder('property')
      .select('property.city', 'city')
      .addSelect('COUNT(*)', 'count')
      .groupBy('property.city')
      .orderBy('count', 'DESC')
      .limit(10)
      .getRawMany();
  }

  private async getPropertyStatusDistribution(): Promise<any[]> {
    return this.propertyRepository
      .createQueryBuilder('property')
      .select('property.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('property.status')
      .getRawMany();
  }

  private async getMonthlyListings(): Promise<any[]> {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    return this.propertyRepository
      .createQueryBuilder('property')
      .select("DATE_TRUNC('month', property.createdAt)", 'month')
      .addSelect('COUNT(*)', 'count')
      .where('property.createdAt >= :sixMonthsAgo', { sixMonthsAgo })
      .groupBy("DATE_TRUNC('month', property.createdAt)")
      .orderBy('month', 'ASC')
      .getRawMany();
  }

  private async getUserRoleDistribution(): Promise<any[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .select('user.role', 'role')
      .addSelect('COUNT(*)', 'count')
      .groupBy('user.role')
      .getRawMany();
  }

  private async getMonthlyRegistrations(): Promise<any[]> {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    return this.userRepository
      .createQueryBuilder('user')
      .select("DATE_TRUNC('month', user.createdAt)", 'month')
      .addSelect('COUNT(*)', 'count')
      .where('user.createdAt >= :sixMonthsAgo', { sixMonthsAgo })
      .groupBy("DATE_TRUNC('month', user.createdAt)")
      .orderBy('month', 'ASC')
      .getRawMany();
  }

  private async getUserActivityStats(): Promise<any> {
    const [activeUsers, inactiveUsers, verifiedUsers] = await Promise.all([
      this.userRepository.count({ where: { isActive: true } }),
      this.userRepository.count({ where: { isActive: false } }),
      this.userRepository.count({ where: { isEmailVerified: true } }),
    ]);

    return {
      activeUsers,
      inactiveUsers,
      verifiedUsers,
    };
  }

  private async getTopLandlords(): Promise<any[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.landlordProperties', 'property')
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'COUNT(property.id) as propertyCount',
      ])
      .where('user.role = :role', { role: UserRole.LANDLORD })
      .groupBy('user.id')
      .orderBy('propertyCount', 'DESC')
      .limit(10)
      .getRawMany();
  }

  private async getTopAgents(): Promise<any[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.agentProperties', 'property')
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'COUNT(property.id) as propertyCount',
      ])
      .where('user.role = :role', { role: UserRole.AGENT })
      .groupBy('user.id')
      .orderBy('propertyCount', 'DESC')
      .limit(10)
      .getRawMany();
  }

  private async getPaymentTypeDistribution(): Promise<any[]> {
    return this.paymentRepository
      .createQueryBuilder('payment')
      .select('payment.paymentType', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('payment.paymentType')
      .getRawMany();
  }

  private async getPaymentStatusDistribution(): Promise<any[]> {
    return this.paymentRepository
      .createQueryBuilder('payment')
      .select('payment.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('payment.status')
      .getRawMany();
  }

  private async getMonthlyPayments(): Promise<any[]> {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    return this.paymentRepository
      .createQueryBuilder('payment')
      .select("DATE_TRUNC('month', payment.createdAt)", 'month')
      .addSelect('COUNT(*)', 'count')
      .addSelect('SUM(payment.amount)', 'amount')
      .where('payment.createdAt >= :sixMonthsAgo', { sixMonthsAgo })
      .groupBy("DATE_TRUNC('month', payment.createdAt)")
      .orderBy('month', 'ASC')
      .getRawMany();
  }

  private async getRevenueByType(): Promise<any[]> {
    return this.paymentRepository
      .createQueryBuilder('payment')
      .select('payment.paymentType', 'type')
      .addSelect('SUM(payment.amount)', 'revenue')
      .where('payment.status = :status', { status: PaymentStatus.COMPLETED })
      .groupBy('payment.paymentType')
      .orderBy('revenue', 'DESC')
      .getRawMany();
  }

  private async getPaymentTrends(): Promise<any> {
    const thisMonth = await this.getMonthlyRevenue();
    const lastMonth = await this.getLastMonthRevenue();
    const growth = lastMonth > 0 ? ((thisMonth - lastMonth) / lastMonth) * 100 : 0;

    return {
      thisMonth,
      lastMonth,
      growth,
    };
  }

  private async getLastMonthRevenue(): Promise<number> {
    const startOfLastMonth = new Date();
    startOfLastMonth.setMonth(startOfLastMonth.getMonth() - 1);
    startOfLastMonth.setDate(1);
    startOfLastMonth.setHours(0, 0, 0, 0);

    const endOfLastMonth = new Date();
    endOfLastMonth.setDate(1);
    endOfLastMonth.setHours(0, 0, 0, 0);

    const result = await this.paymentRepository
      .createQueryBuilder('payment')
      .select('SUM(payment.amount)', 'total')
      .where('payment.status = :status', { status: PaymentStatus.COMPLETED })
      .andWhere('payment.createdAt >= :start AND payment.createdAt < :end', {
        start: startOfLastMonth,
        end: endOfLastMonth,
      })
      .getRawOne();

    return parseFloat(result.total) || 0;
  }

  private async getAveragePrices(): Promise<any> {
    const result = await this.propertyRepository
      .createQueryBuilder('property')
      .select('property.propertyType', 'type')
      .addSelect('AVG(property.pricePerYear)', 'averagePrice')
      .groupBy('property.propertyType')
      .getRawMany();

    return result.map(item => ({
      type: item.type,
      averagePrice: parseFloat(item.averagePrice) || 0,
    }));
  }

  private async getPriceTrends(): Promise<any[]> {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    return this.propertyRepository
      .createQueryBuilder('property')
      .select("DATE_TRUNC('month', property.createdAt)", 'month')
      .addSelect('AVG(property.pricePerYear)', 'averagePrice')
      .where('property.createdAt >= :sixMonthsAgo', { sixMonthsAgo })
      .groupBy("DATE_TRUNC('month', property.createdAt)")
      .orderBy('month', 'ASC')
      .getRawMany();
  }

  private async getDemandAnalysis(): Promise<any> {
    const [totalViews, totalInquiries, topViewedProperties] = await Promise.all([
      this.propertyRepository
        .createQueryBuilder('property')
        .select('SUM(property.viewsCount)', 'total')
        .getRawOne()
        .then(result => parseInt(result.total) || 0),
      this.propertyRepository
        .createQueryBuilder('property')
        .select('SUM(property.inquiriesCount)', 'total')
        .getRawOne()
        .then(result => parseInt(result.total) || 0),
      this.propertyRepository
        .createQueryBuilder('property')
        .select(['property.id', 'property.title', 'property.viewsCount', 'property.inquiriesCount'])
        .orderBy('property.viewsCount', 'DESC')
        .limit(10)
        .getMany(),
    ]);

    return {
      totalViews,
      totalInquiries,
      conversionRate: totalViews > 0 ? (totalInquiries / totalViews) * 100 : 0,
      topViewedProperties,
    };
  }

  private async getPopularAmenities(): Promise<any[]> {
    const properties = await this.propertyRepository
      .createQueryBuilder('property')
      .select('property.amenities')
      .getMany();

    const amenityCount = {};
    properties.forEach(property => {
      property.amenities.forEach(amenity => {
        amenityCount[amenity] = (amenityCount[amenity] || 0) + 1;
      });
    });

    return Object.entries(amenityCount)
      .map(([amenity, count]) => ({ amenity, count }))
      .sort((a, b) => (b.count as number) - (a.count as number))
      .slice(0, 10);
  }

  private async getMarketActivity(): Promise<any> {
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [weeklyListings, monthlyListings, weeklyInquiries, monthlyInquiries] = await Promise.all([
      this.propertyRepository.count({ where: { createdAt: { gte: lastWeek } as any } }),
      this.propertyRepository.count({ where: { createdAt: { gte: lastMonth } as any } }),
      this.propertyRepository
        .createQueryBuilder('property')
        .select('SUM(property.inquiriesCount)', 'total')
        .where('property.updatedAt >= :lastWeek', { lastWeek })
        .getRawOne()
        .then(result => parseInt(result.total) || 0),
      this.propertyRepository
        .createQueryBuilder('property')
        .select('SUM(property.inquiriesCount)', 'total')
        .where('property.updatedAt >= :lastMonth', { lastMonth })
        .getRawOne()
        .then(result => parseInt(result.total) || 0),
    ]);

    return {
      weeklyListings,
      monthlyListings,
      weeklyInquiries,
      monthlyInquiries,
    };
  }
}
