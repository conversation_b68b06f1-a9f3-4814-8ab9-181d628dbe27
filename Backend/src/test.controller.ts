import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Test')
@Controller('test')
export class TestController {
  @Get('health')
  @ApiOperation({ summary: 'Basic health check' })
  @ApiResponse({
    status: 200,
    description: 'Server is running',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2023-12-01T10:00:00Z' },
        uptime: { type: 'number', example: 123.45 },
        message: { type: 'string', example: 'PHCityRent Backend is running successfully!' },
      },
    },
  })
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      message: 'PHCityRent Backend is running successfully!',
    };
  }

  @Get('endpoints')
  @ApiOperation({ summary: 'List all available endpoints' })
  @ApiResponse({
    status: 200,
    description: 'Available endpoints',
    schema: {
      type: 'object',
      properties: {
        totalEndpoints: { type: 'number', example: 84 },
        modules: {
          type: 'object',
          properties: {
            authentication: { type: 'array', items: { type: 'string' } },
            users: { type: 'array', items: { type: 'string' } },
            properties: { type: 'array', items: { type: 'string' } },
            agents: { type: 'array', items: { type: 'string' } },
            payments: { type: 'array', items: { type: 'string' } },
            analytics: { type: 'array', items: { type: 'string' } },
            admin: { type: 'array', items: { type: 'string' } },
            files: { type: 'array', items: { type: 'string' } },
            notifications: { type: 'array', items: { type: 'string' } },
            health: { type: 'array', items: { type: 'string' } },
          },
        },
      },
    },
  })
  getEndpoints() {
    const endpoints = {
      authentication: [
        'POST /api/v1/auth/register',
        'POST /api/v1/auth/login',
        'POST /api/v1/auth/refresh',
        'POST /api/v1/auth/logout',
        'PATCH /api/v1/auth/change-password',
        'POST /api/v1/auth/forgot-password',
        'POST /api/v1/auth/reset-password',
        'GET /api/v1/auth/me',
      ],
      users: [
        'GET /api/v1/users',
        'GET /api/v1/users/profile',
        'GET /api/v1/users/:id',
        'PATCH /api/v1/users/profile',
        'PATCH /api/v1/users/:id',
        'PATCH /api/v1/users/:id/activate',
        'PATCH /api/v1/users/:id/deactivate',
        'DELETE /api/v1/users/:id',
      ],
      properties: [
        'POST /api/v1/properties',
        'GET /api/v1/properties',
        'GET /api/v1/properties/my-properties',
        'GET /api/v1/properties/stats',
        'GET /api/v1/properties/:id',
        'PATCH /api/v1/properties/:id',
        'PATCH /api/v1/properties/:id/status',
        'PATCH /api/v1/properties/:id/toggle-featured',
        'PATCH /api/v1/properties/:id/toggle-verified',
        'POST /api/v1/properties/:id/inquire',
        'DELETE /api/v1/properties/:id',
      ],
      agents: [
        'GET /api/v1/agents',
        'GET /api/v1/agents/top',
        'GET /api/v1/agents/search',
        'GET /api/v1/agents/:id',
        'GET /api/v1/agents/:id/properties',
        'GET /api/v1/agents/:id/stats',
        'PATCH /api/v1/agents/:id/activate',
        'PATCH /api/v1/agents/:id/deactivate',
      ],
      payments: [
        'POST /api/v1/payments',
        'GET /api/v1/payments',
        'GET /api/v1/payments/stats',
        'GET /api/v1/payments/overdue',
        'GET /api/v1/payments/by-type/:type',
        'GET /api/v1/payments/:id',
        'PATCH /api/v1/payments/:id/status',
        'POST /api/v1/payments/:id/process',
      ],
      analytics: [
        'GET /api/v1/analytics/dashboard',
        'GET /api/v1/analytics/properties',
        'GET /api/v1/analytics/users',
        'GET /api/v1/analytics/payments',
        'GET /api/v1/analytics/market-insights',
      ],
      admin: [
        'GET /api/v1/admin/overview',
        'GET /api/v1/admin/users',
        'GET /api/v1/admin/properties',
        'GET /api/v1/admin/payments',
        'GET /api/v1/admin/pending-approvals',
        'GET /api/v1/admin/recent-activity',
        'PATCH /api/v1/admin/users/:id/suspend',
        'PATCH /api/v1/admin/users/:id/activate',
        'PATCH /api/v1/admin/properties/:id/verify',
        'PATCH /api/v1/admin/properties/:id/unverify',
        'PATCH /api/v1/admin/properties/:id/feature',
        'PATCH /api/v1/admin/properties/:id/unfeature',
        'PATCH /api/v1/admin/payments/:id/approve',
        'PATCH /api/v1/admin/payments/:id/reject',
        'POST /api/v1/admin/bulk-actions',
      ],
      files: [
        'POST /api/v1/files/upload/image',
        'POST /api/v1/files/upload/images',
        'POST /api/v1/files/upload/document',
        'GET /api/v1/files/images/:userId/:filename',
        'GET /api/v1/files/documents/:userId/:filename',
        'GET /api/v1/files/my-files',
        'GET /api/v1/files/my-files/stats',
        'DELETE /api/v1/files/images/:filename',
        'DELETE /api/v1/files/documents/:filename',
      ],
      notifications: [
        'GET /api/v1/notifications',
        'GET /api/v1/notifications/my-notifications',
        'GET /api/v1/notifications/unread-count',
        'GET /api/v1/notifications/stats',
        'GET /api/v1/notifications/by-type/:type',
        'GET /api/v1/notifications/:id',
        'PATCH /api/v1/notifications/:id/read',
        'POST /api/v1/notifications/mark-all-read',
        'POST /api/v1/notifications/retry-failed',
      ],
      health: [
        'GET /api/v1/health',
        'GET /api/v1/health/database',
        'GET /api/v1/health/memory',
        'GET /api/v1/health/disk',
      ],
      test: [
        'GET /api/v1/test/health',
        'GET /api/v1/test/endpoints',
      ],
    };

    const totalEndpoints = Object.values(endpoints).reduce((total, moduleEndpoints) => total + moduleEndpoints.length, 0);

    return {
      totalEndpoints,
      modules: endpoints,
      message: `PHCityRent API has ${totalEndpoints} endpoints across ${Object.keys(endpoints).length} modules`,
    };
  }
}
