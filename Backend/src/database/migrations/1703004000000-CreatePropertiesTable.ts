import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreatePropertiesTable1703004000000 implements MigrationInterface {
  name = 'CreatePropertiesTable1703004000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum types
    await queryRunner.query(`
      CREATE TYPE "property_type_enum" AS ENUM(
        'apartment', 'house', 'duplex', 'bungalow', 'flat', 
        'room', 'self_contain', 'shop', 'office', 'warehouse'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "property_status_enum" AS ENUM('available', 'rented', 'maintenance', 'inactive')
    `);

    await queryRunner.query(`
      CREATE TYPE "furnishing_status_enum" AS ENUM('furnished', 'semi_furnished', 'unfurnished')
    `);

    // Create properties table
    await queryRunner.createTable(
      new Table({
        name: 'properties',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'title',
            type: 'varchar',
            length: '200',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'propertyType',
            type: 'property_type_enum',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'property_status_enum',
            default: "'available'",
          },
          {
            name: 'pricePerYear',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'pricePerMonth',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'securityDeposit',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'agentCommission',
            type: 'decimal',
            precision: 5,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'location',
            type: 'varchar',
            length: '500',
            isNullable: false,
          },
          {
            name: 'city',
            type: 'varchar',
            default: "'Port Harcourt'",
          },
          {
            name: 'state',
            type: 'varchar',
            default: "'Rivers'",
          },
          {
            name: 'country',
            type: 'varchar',
            default: "'Nigeria'",
          },
          {
            name: 'bedrooms',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'bathrooms',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'toilets',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'sizeInSqm',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'furnishingStatus',
            type: 'furnishing_status_enum',
            isNullable: true,
          },
          {
            name: 'amenities',
            type: 'text',
            array: true,
            default: 'ARRAY[]::text[]',
          },
          {
            name: 'images',
            type: 'text',
            array: true,
            default: 'ARRAY[]::text[]',
          },
          {
            name: 'videoUrl',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'virtualTourUrl',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'latitude',
            type: 'decimal',
            precision: 10,
            scale: 8,
            isNullable: true,
          },
          {
            name: 'longitude',
            type: 'decimal',
            precision: 11,
            scale: 8,
            isNullable: true,
          },
          {
            name: 'isFeatured',
            type: 'boolean',
            default: false,
          },
          {
            name: 'isVerified',
            type: 'boolean',
            default: false,
          },
          {
            name: 'viewsCount',
            type: 'integer',
            default: 0,
          },
          {
            name: 'inquiriesCount',
            type: 'integer',
            default: 0,
          },
          {
            name: 'landlord_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'agent_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'properties',
      new Index('IDX_properties_location', ['location']),
    );

    await queryRunner.createIndex(
      'properties',
      new Index('IDX_properties_propertyType', ['propertyType']),
    );

    await queryRunner.createIndex(
      'properties',
      new Index('IDX_properties_status', ['status']),
    );

    await queryRunner.createIndex(
      'properties',
      new Index('IDX_properties_pricePerYear', ['pricePerYear']),
    );

    await queryRunner.createIndex(
      'properties',
      new Index('IDX_properties_bedrooms_bathrooms', ['bedrooms', 'bathrooms']),
    );

    await queryRunner.createIndex(
      'properties',
      new Index('IDX_properties_isFeatured', ['isFeatured']),
    );

    await queryRunner.createIndex(
      'properties',
      new Index('IDX_properties_isVerified', ['isVerified']),
    );

    await queryRunner.createIndex(
      'properties',
      new Index('IDX_properties_landlord_id', ['landlord_id']),
    );

    await queryRunner.createIndex(
      'properties',
      new Index('IDX_properties_agent_id', ['agent_id']),
    );

    await queryRunner.createIndex(
      'properties',
      new Index('IDX_properties_createdAt', ['createdAt']),
    );

    // Create foreign keys
    await queryRunner.createForeignKey(
      'properties',
      new ForeignKey({
        columnNames: ['landlord_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'properties',
      new ForeignKey({
        columnNames: ['agent_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      }),
    );

    // Create trigger for updated_at
    await queryRunner.query(`
      CREATE TRIGGER update_properties_updated_at 
      BEFORE UPDATE ON properties 
      FOR EACH ROW 
      EXECUTE FUNCTION update_updated_at_column();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TRIGGER IF EXISTS update_properties_updated_at ON properties');
    await queryRunner.dropTable('properties');
    await queryRunner.query('DROP TYPE "furnishing_status_enum"');
    await queryRunner.query('DROP TYPE "property_status_enum"');
    await queryRunner.query('DROP TYPE "property_type_enum"');
  }
}
