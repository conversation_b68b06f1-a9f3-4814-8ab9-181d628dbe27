import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateUsersTable1703001000000 implements MigrationInterface {
  name = 'CreateUsersTable1703001000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum types
    await queryRunner.query(`
      CREATE TYPE "user_role_enum" AS ENUM('admin', 'agent', 'landlord', 'tenant')
    `);

    await queryRunner.query(`
      CREATE TYPE "user_status_enum" AS ENUM('active', 'inactive', 'suspended', 'pending_verification')
    `);

    // Create users table
    await queryRunner.createTable(
      new Table({
        name: 'users',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'email',
            type: 'varchar',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'password',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'firstName',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'lastName',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'phone',
            type: 'varchar',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'role',
            type: 'user_role_enum',
            default: "'tenant'",
          },
          {
            name: 'status',
            type: 'user_status_enum',
            default: "'pending_verification'",
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'isEmailVerified',
            type: 'boolean',
            default: false,
          },
          {
            name: 'isPhoneVerified',
            type: 'boolean',
            default: false,
          },
          {
            name: 'avatar',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'dateOfBirth',
            type: 'date',
            isNullable: true,
          },
          {
            name: 'gender',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'address',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'city',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'state',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'country',
            type: 'varchar',
            default: "'Nigeria'",
          },
          {
            name: 'occupation',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'monthlyIncome',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'preferredLanguage',
            type: 'varchar',
            default: "'en'",
          },
          {
            name: 'timezone',
            type: 'varchar',
            default: "'Africa/Lagos'",
          },
          {
            name: 'emailNotifications',
            type: 'boolean',
            default: true,
          },
          {
            name: 'smsNotifications',
            type: 'boolean',
            default: true,
          },
          {
            name: 'pushNotifications',
            type: 'boolean',
            default: true,
          },
          {
            name: 'emailVerificationToken',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'emailVerificationExpires',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'phoneVerificationToken',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'phoneVerificationExpires',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'passwordResetToken',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'passwordResetExpires',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'lastLoginAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'lastLoginIp',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'failedLoginAttempts',
            type: 'integer',
            default: 0,
          },
          {
            name: 'lockedUntil',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'users',
      new Index('IDX_users_email', ['email']),
    );

    await queryRunner.createIndex(
      'users',
      new Index('IDX_users_phone', ['phone']),
    );

    await queryRunner.createIndex(
      'users',
      new Index('IDX_users_role', ['role']),
    );

    await queryRunner.createIndex(
      'users',
      new Index('IDX_users_status', ['status']),
    );

    await queryRunner.createIndex(
      'users',
      new Index('IDX_users_isActive', ['isActive']),
    );

    await queryRunner.createIndex(
      'users',
      new Index('IDX_users_createdAt', ['createdAt']),
    );

    // Create trigger for updated_at
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updatedAt = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    await queryRunner.query(`
      CREATE TRIGGER update_users_updated_at 
      BEFORE UPDATE ON users 
      FOR EACH ROW 
      EXECUTE FUNCTION update_updated_at_column();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TRIGGER IF EXISTS update_users_updated_at ON users');
    await queryRunner.query('DROP FUNCTION IF EXISTS update_updated_at_column()');
    await queryRunner.dropTable('users');
    await queryRunner.query('DROP TYPE "user_status_enum"');
    await queryRunner.query('DROP TYPE "user_role_enum"');
  }
}
