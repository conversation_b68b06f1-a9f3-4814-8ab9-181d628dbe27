import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateLoginAttemptsTable1703003000000 implements MigrationInterface {
  name = 'CreateLoginAttemptsTable1703003000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create login_attempts table
    await queryRunner.createTable(
      new Table({
        name: 'login_attempts',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'email',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'ipAddress',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'success',
            type: 'boolean',
            default: false,
          },
          {
            name: 'userAgent',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'failureReason',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'login_attempts',
      new Index('IDX_login_attempts_email', ['email']),
    );

    await queryRunner.createIndex(
      'login_attempts',
      new Index('IDX_login_attempts_ipAddress', ['ipAddress']),
    );

    await queryRunner.createIndex(
      'login_attempts',
      new Index('IDX_login_attempts_email_ipAddress', ['email', 'ipAddress']),
    );

    await queryRunner.createIndex(
      'login_attempts',
      new Index('IDX_login_attempts_createdAt', ['createdAt']),
    );

    await queryRunner.createIndex(
      'login_attempts',
      new Index('IDX_login_attempts_success', ['success']),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('login_attempts');
  }
}
