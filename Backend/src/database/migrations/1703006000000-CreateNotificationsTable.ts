import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreateNotificationsTable1703006000000 implements MigrationInterface {
  name = 'CreateNotificationsTable1703006000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum types
    await queryRunner.query(`
      CREATE TYPE "notification_type_enum" AS ENUM(
        'property_inquiry', 'payment_received', 'payment_due', 'property_approved', 
        'property_rejected', 'user_registered', 'system_alert', 'marketing'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "notification_channel_enum" AS ENUM('email', 'sms', 'push', 'in_app')
    `);

    await queryRunner.query(`
      CREATE TYPE "notification_status_enum" AS ENUM('pending', 'sent', 'delivered', 'failed', 'read')
    `);

    // Create notifications table
    await queryRunner.createTable(
      new Table({
        name: 'notifications',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'type',
            type: 'notification_type_enum',
            isNullable: false,
          },
          {
            name: 'channel',
            type: 'notification_channel_enum',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'notification_status_enum',
            default: "'pending'",
          },
          {
            name: 'title',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'message',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'recipientEmail',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'recipientPhone',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'pushToken',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'data',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'actionUrl',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'sentAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'deliveredAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'readAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'retryCount',
            type: 'integer',
            default: 0,
          },
          {
            name: 'errorMessage',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'notifications',
      new Index('IDX_notifications_type', ['type']),
    );

    await queryRunner.createIndex(
      'notifications',
      new Index('IDX_notifications_channel', ['channel']),
    );

    await queryRunner.createIndex(
      'notifications',
      new Index('IDX_notifications_status', ['status']),
    );

    await queryRunner.createIndex(
      'notifications',
      new Index('IDX_notifications_user_id', ['user_id']),
    );

    await queryRunner.createIndex(
      'notifications',
      new Index('IDX_notifications_createdAt', ['createdAt']),
    );

    await queryRunner.createIndex(
      'notifications',
      new Index('IDX_notifications_sentAt', ['sentAt']),
    );

    await queryRunner.createIndex(
      'notifications',
      new Index('IDX_notifications_readAt', ['readAt']),
    );

    // Create foreign key
    await queryRunner.createForeignKey(
      'notifications',
      new ForeignKey({
        columnNames: ['user_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    // Create trigger for updated_at
    await queryRunner.query(`
      CREATE TRIGGER update_notifications_updated_at 
      BEFORE UPDATE ON notifications 
      FOR EACH ROW 
      EXECUTE FUNCTION update_updated_at_column();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TRIGGER IF EXISTS update_notifications_updated_at ON notifications');
    await queryRunner.dropTable('notifications');
    await queryRunner.query('DROP TYPE "notification_status_enum"');
    await queryRunner.query('DROP TYPE "notification_channel_enum"');
    await queryRunner.query('DROP TYPE "notification_type_enum"');
  }
}
