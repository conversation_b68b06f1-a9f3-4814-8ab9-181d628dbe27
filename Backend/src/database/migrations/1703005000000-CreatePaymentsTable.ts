import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreatePaymentsTable1703005000000 implements MigrationInterface {
  name = 'CreatePaymentsTable1703005000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum types
    await queryRunner.query(`
      CREATE TYPE "payment_type_enum" AS ENUM('rent', 'deposit', 'commission', 'service_fee', 'subscription')
    `);

    await queryRunner.query(`
      CREATE TYPE "payment_status_enum" AS ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')
    `);

    await queryRunner.query(`
      CREATE TYPE "payment_gateway_enum" AS ENUM('paystack', 'flutterwave', 'bank_transfer', 'cash')
    `);

    // Create payments table
    await queryRunner.createTable(
      new Table({
        name: 'payments',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'reference',
            type: 'varchar',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'paymentType',
            type: 'payment_type_enum',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'payment_status_enum',
            default: "'pending'",
          },
          {
            name: 'gateway',
            type: 'payment_gateway_enum',
            isNullable: false,
          },
          {
            name: 'amount',
            type: 'decimal',
            precision: 15,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'currency',
            type: 'varchar',
            default: "'NGN'",
          },
          {
            name: 'description',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'gatewayReference',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'gatewayResponse',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'dueDate',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'paidAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'failureReason',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'property_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'recipient_id',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'payments',
      new Index('IDX_payments_reference', ['reference']),
    );

    await queryRunner.createIndex(
      'payments',
      new Index('IDX_payments_status', ['status']),
    );

    await queryRunner.createIndex(
      'payments',
      new Index('IDX_payments_paymentType', ['paymentType']),
    );

    await queryRunner.createIndex(
      'payments',
      new Index('IDX_payments_gateway', ['gateway']),
    );

    await queryRunner.createIndex(
      'payments',
      new Index('IDX_payments_user_id', ['user_id']),
    );

    await queryRunner.createIndex(
      'payments',
      new Index('IDX_payments_property_id', ['property_id']),
    );

    await queryRunner.createIndex(
      'payments',
      new Index('IDX_payments_recipient_id', ['recipient_id']),
    );

    await queryRunner.createIndex(
      'payments',
      new Index('IDX_payments_createdAt', ['createdAt']),
    );

    // Create foreign keys
    await queryRunner.createForeignKey(
      'payments',
      new ForeignKey({
        columnNames: ['user_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'payments',
      new ForeignKey({
        columnNames: ['property_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'properties',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'payments',
      new ForeignKey({
        columnNames: ['recipient_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      }),
    );

    // Create trigger for updated_at
    await queryRunner.query(`
      CREATE TRIGGER update_payments_updated_at 
      BEFORE UPDATE ON payments 
      FOR EACH ROW 
      EXECUTE FUNCTION update_updated_at_column();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TRIGGER IF EXISTS update_payments_updated_at ON payments');
    await queryRunner.dropTable('payments');
    await queryRunner.query('DROP TYPE "payment_gateway_enum"');
    await queryRunner.query('DROP TYPE "payment_status_enum"');
    await queryRunner.query('DROP TYPE "payment_type_enum"');
  }
}
