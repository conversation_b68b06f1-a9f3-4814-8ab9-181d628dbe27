import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';
import { UserSeeder } from './user.seeder';

// Load environment variables
config();

async function runSeeders() {
  console.log('🌱 Starting database seeding...');

  // Create data source
  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'phcityrent',
    entities: [__dirname + '/../../**/*.entity{.ts,.js}'],
    synchronize: false,
    logging: false,
  });

  try {
    // Initialize data source
    await dataSource.initialize();
    console.log('📊 Database connection established');

    // Run seeders
    await UserSeeder.run(dataSource);

    console.log('🎉 All seeders completed successfully!');
  } catch (error) {
    console.error('❌ Error running seeders:', error);
    process.exit(1);
  } finally {
    // Close connection
    await dataSource.destroy();
    console.log('🔌 Database connection closed');
  }
}

// Run seeders if this file is executed directly
if (require.main === module) {
  runSeeders();
}

export { runSeeders };
