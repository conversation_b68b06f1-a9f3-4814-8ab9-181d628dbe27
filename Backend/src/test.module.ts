import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TestController } from './test.controller';

// Import all controllers for Swagger documentation
import { AuthController } from './modules/auth/auth.controller';
import { UsersController } from './modules/users/users.controller';
import { HealthController } from './modules/health/health.controller';
import { PropertiesController } from './api/v1/properties/properties.controller';
import { AgentsController } from './api/v1/agents/agents.controller';
import { PaymentsController } from './api/v1/payments/payments.controller';
import { AnalyticsController } from './api/v1/analytics/analytics.controller';
import { AdminController } from './api/v1/admin/admin.controller';
import { FilesController } from './api/v1/files/files.controller';
import { NotificationsController } from './api/v1/notifications/notifications.controller';

// Mock services for controllers
import { AuthService } from './modules/auth/auth.service';
import { UsersService } from './modules/users/users.service';
import { HealthService } from './modules/health/health.service';
import { PropertiesService } from './api/v1/properties/properties.service';
import { AgentsService } from './api/v1/agents/agents.service';
import { PaymentsService } from './api/v1/payments/payments.service';
import { AnalyticsService } from './api/v1/analytics/analytics.service';
import { AdminService } from './api/v1/admin/admin.service';
import { FilesService } from './api/v1/files/files.service';
import { NotificationsService } from './api/v1/notifications/notifications.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
  ],
  controllers: [
    TestController,
  ],
  providers: [],
})
export class TestModule {}
