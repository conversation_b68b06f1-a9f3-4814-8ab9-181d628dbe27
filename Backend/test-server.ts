import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { TestModule } from './src/test.module';

async function bootstrap() {
  try {
    console.log('🚀 Starting PHCityRent Backend...');
    
    const app = await NestFactory.create(TestModule);

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    // CORS configuration
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true,
    });

    // API prefix
    app.setGlobalPrefix('api/v1');

    // Swagger documentation
    const config = new DocumentBuilder()
      .setTitle('PHCityRent API')
      .setDescription('Comprehensive Real Estate Platform API for Port Harcourt')
      .setVersion('1.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addTag('Authentication', 'User authentication and authorization')
      .addTag('Users', 'User management')
      .addTag('Properties', 'Property management')
      .addTag('Agents', 'Real estate agent management')
      .addTag('Payments', 'Payment processing')
      .addTag('Analytics', 'Analytics and reporting')
      .addTag('Admin', 'Administrative functions')
      .addTag('Files', 'File upload and management')
      .addTag('Notifications', 'Notification system')
      .addTag('Health', 'System health monitoring')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
    });

    const port = process.env.PORT || 3001;
    await app.listen(port);

    console.log('✅ PHCityRent Backend started successfully!');
    console.log(`🌐 Server running on: http://localhost:${port}`);
    console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
    console.log(`🏥 Health Check: http://localhost:${port}/api/v1/health`);
    console.log('');
    console.log('📋 Available Endpoints:');
    console.log('  Authentication: /api/v1/auth/*');
    console.log('  Users: /api/v1/users/*');
    console.log('  Properties: /api/v1/properties/*');
    console.log('  Agents: /api/v1/agents/*');
    console.log('  Payments: /api/v1/payments/*');
    console.log('  Analytics: /api/v1/analytics/*');
    console.log('  Admin: /api/v1/admin/*');
    console.log('  Files: /api/v1/files/*');
    console.log('  Notifications: /api/v1/notifications/*');
    console.log('  Health: /api/v1/health/*');

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

bootstrap();
