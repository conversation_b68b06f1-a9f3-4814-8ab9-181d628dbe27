# PHCityRent Backend API

Enterprise-grade NestJS backend for the PHCityRent platform with comprehensive authentication, authorization, and database management.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm 8+
- PostgreSQL 15+
- Redis 6+

### Installation

```bash
# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Edit .env with your configuration
nano .env

# Run database migrations
npm run migration:run

# Seed the database
npm run seed

# Start development server
npm run start:dev
```

The API will be available at `http://localhost:3001` with Swagger documentation at `http://localhost:3001/api/docs`.

## 📁 Project Structure

```
Backend/
├── src/
│   ├── modules/           # Feature modules
│   │   ├── auth/         # Authentication & authorization
│   │   ├── users/        # User management
│   │   ├── properties/   # Property management (placeholder)
│   │   ├── agents/       # Agent management (placeholder)
│   │   ├── payments/     # Payment processing (placeholder)
│   │   ├── analytics/    # Analytics & reporting (placeholder)
│   │   ├── admin/        # Admin functionality (placeholder)
│   │   ├── notifications/ # Notification system (placeholder)
│   │   ├── files/        # File management (placeholder)
│   │   └── health/       # Health checks
│   ├── common/           # Shared utilities
│   │   ├── decorators/   # Custom decorators
│   │   ├── filters/      # Exception filters
│   │   ├── interceptors/ # Request/response interceptors
│   │   ├── pipes/        # Validation pipes
│   │   ├── utils/        # Utility functions
│   │   └── logger/       # Logging service
│   ├── config/           # Configuration files
│   ├── database/         # Database migrations and seeds
│   └── main.ts           # Application entry point
├── database/             # Database optimization scripts
├── docs/                 # Documentation
├── test/                 # Test files
└── package.json          # Dependencies and scripts
```

## 🔧 Key Features Implemented

### ✅ **Authentication & Authorization**
- **JWT Authentication** with access and refresh tokens
- **Role-Based Access Control (RBAC)** with 4 roles: Admin, Agent, Landlord, Tenant
- **Password Security** with bcrypt hashing and complexity requirements
- **Account Security** with login attempt tracking and account lockout
- **Password Reset** with secure token-based reset flow
- **Email/Phone Verification** infrastructure

### ✅ **Database Architecture**
- **PostgreSQL** with TypeORM and connection pooling
- **Database Migrations** with version control
- **Database Seeding** with sample data
- **Optimized Indexes** for performance
- **Foreign Key Constraints** for data integrity

### ✅ **API Documentation**
- **OpenAPI 3.0** specification with Swagger UI
- **Comprehensive Documentation** for all endpoints
- **Request/Response Examples** with validation schemas
- **Authentication Documentation** with bearer token support

### ✅ **Error Handling & Logging**
- **Global Exception Filter** with structured error responses
- **Request/Response Logging** with performance tracking
- **Winston Logger** with file rotation and structured logging
- **Error Tracking** with unique error IDs

### ✅ **Security Features**
- **Helmet** for security headers
- **Rate Limiting** with Redis-based throttling
- **Input Validation** with class-validator
- **CORS Configuration** with environment-based origins
- **SQL Injection Protection** with parameterized queries

### ✅ **Performance & Monitoring**
- **Redis Caching** for session and data caching
- **Connection Pooling** for database optimization
- **Health Checks** with Terminus for monitoring
- **Request Compression** with gzip
- **Performance Interceptors** for slow query detection

## 🛠️ Available Scripts

```bash
# Development
npm run start:dev          # Start development server with hot reload
npm run start:debug        # Start with debugging enabled

# Production
npm run build              # Build for production
npm run start:prod         # Start production server

# Database
npm run migration:generate # Generate new migration
npm run migration:run      # Run pending migrations
npm run migration:revert   # Revert last migration
npm run seed              # Seed database with sample data
npm run db:reset          # Reset database (revert, run, seed)

# Testing
npm run test              # Run unit tests
npm run test:watch        # Run tests in watch mode
npm run test:cov          # Run tests with coverage
npm run test:e2e          # Run end-to-end tests

# Code Quality
npm run lint              # Lint code
npm run format            # Format code with Prettier
```

## 🔐 Authentication Flow

### Registration
```bash
POST /api/v1/auth/register
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+*************",
  "role": "tenant"
}
```

### Login
```bash
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

### Protected Routes
```bash
GET /api/v1/users/profile
Authorization: Bearer <access_token>
```

## 👥 Default User Accounts

After running `npm run seed`, the following accounts are available:

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| Admin | <EMAIL> | Admin123!@# | System administrator |
| Agent | <EMAIL> | Admin123!@# | Real estate agent |
| Landlord | <EMAIL> | Admin123!@# | Property owner |
| Tenant | <EMAIL> | Admin123!@# | Property seeker |

## 🌐 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout
- `PATCH /api/v1/auth/change-password` - Change password
- `POST /api/v1/auth/forgot-password` - Request password reset
- `POST /api/v1/auth/reset-password` - Reset password with token
- `GET /api/v1/auth/me` - Get current user profile

### Users
- `GET /api/v1/users` - Get all users (Admin/Agent only)
- `GET /api/v1/users/profile` - Get current user profile
- `GET /api/v1/users/:id` - Get user by ID (Admin/Agent only)
- `PATCH /api/v1/users/profile` - Update current user profile
- `PATCH /api/v1/users/:id` - Update user by ID (Admin only)
- `PATCH /api/v1/users/:id/activate` - Activate user (Admin only)
- `PATCH /api/v1/users/:id/deactivate` - Deactivate user (Admin only)
- `DELETE /api/v1/users/:id` - Delete user (Admin only)

### Health Checks
- `GET /api/v1/health` - Comprehensive health check
- `GET /api/v1/health/database` - Database health check
- `GET /api/v1/health/memory` - Memory health check
- `GET /api/v1/health/disk` - Disk health check

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Environment | development | No |
| `PORT` | Server port | 3001 | No |
| `DB_HOST` | Database host | localhost | Yes |
| `DB_PORT` | Database port | 5432 | No |
| `DB_USERNAME` | Database username | postgres | Yes |
| `DB_PASSWORD` | Database password | - | Yes |
| `DB_NAME` | Database name | phcityrent | Yes |
| `REDIS_HOST` | Redis host | localhost | No |
| `REDIS_PORT` | Redis port | 6379 | No |
| `JWT_SECRET` | JWT secret key | - | Yes |
| `JWT_REFRESH_SECRET` | JWT refresh secret | - | Yes |

See `.env.example` for complete configuration options.

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:cov

# Run E2E tests
npm run test:e2e

# Run tests in watch mode
npm run test:watch
```

## 📊 Monitoring & Health Checks

The API includes comprehensive health checks accessible at:

- `/health` - Overall system health
- `/health/database` - Database connectivity
- `/health/memory` - Memory usage
- `/health/disk` - Disk space

## 🚀 Deployment

### Docker Deployment
```bash
# Build Docker image
docker build -t phcityrent-api .

# Run with Docker Compose
docker-compose up -d
```

### Production Deployment
```bash
# Build for production
npm run build

# Start production server
npm run start:prod
```

## 🔄 Database Migrations

```bash
# Generate migration
npm run migration:generate -- --name=CreateNewTable

# Run migrations
npm run migration:run

# Revert migration
npm run migration:revert
```

## 📚 Additional Documentation

- **API Documentation**: Available at `/api/docs` when running
- **Database Schema**: See `src/database/migrations/`
- **Configuration**: See `src/config/`
- **Authentication**: See `src/modules/auth/`

## 🤝 Contributing

1. Follow TypeScript and NestJS best practices
2. Add tests for new features
3. Update documentation
4. Follow the existing code style
5. Use conventional commits

## 📝 License

This project is proprietary software for PHCityRent platform.

---

**Note**: This is an enterprise-grade backend with production-ready authentication, security, and monitoring features. The modular architecture allows for easy expansion of additional features like property management, payments, and analytics.
