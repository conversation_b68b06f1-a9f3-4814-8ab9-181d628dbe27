#!/bin/bash

# =====================================================
# PHCityRent Production Database Deployment Script
# Complete setup for production-grade PostgreSQL deployment
# =====================================================

set -euo pipefail

# Configuration
DB_VERSION="${DB_VERSION:-15}"
DB_NAME="${DB_NAME:-phcityrent}"
DB_USER="${DB_USER:-phcityrent_user}"
DB_PASSWORD="${DB_PASSWORD:-}"
ADMIN_EMAIL="${ADMIN_EMAIL:-<EMAIL>}"
BACKUP_DIR="${BACKUP_DIR:-/var/backups/postgresql}"
LOG_DIR="${LOG_DIR:-/var/log/phcityrent}"
SSL_CERT_PATH="${SSL_CERT_PATH:-/etc/ssl/certs}"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}"
    echo "${timestamp} [${level}] ${message}" >> "${LOG_DIR}/deployment.log"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error_exit "This script must be run as root"
    fi
}

# Validate configuration
validate_config() {
    log "INFO" "Validating configuration..."
    
    if [[ -z "${DB_PASSWORD}" ]]; then
        error_exit "DB_PASSWORD must be set"
    fi
    
    if [[ ${#DB_PASSWORD} -lt 12 ]]; then
        error_exit "DB_PASSWORD must be at least 12 characters long"
    fi
    
    log "INFO" "Configuration validated"
}

# Install PostgreSQL and extensions
install_postgresql() {
    log "INFO" "Installing PostgreSQL ${DB_VERSION} and extensions..."
    
    # Update package list
    apt-get update
    
    # Install PostgreSQL repository
    apt-get install -y wget ca-certificates
    wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add -
    echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list
    
    # Update package list again
    apt-get update
    
    # Install PostgreSQL and extensions
    apt-get install -y \
        postgresql-${DB_VERSION} \
        postgresql-client-${DB_VERSION} \
        postgresql-contrib-${DB_VERSION} \
        postgresql-${DB_VERSION}-pg-stat-statements \
        postgresql-${DB_VERSION}-pg-buffercache \
        postgresql-${DB_VERSION}-pgstattuple \
        postgresql-${DB_VERSION}-cron \
        postgresql-${DB_VERSION}-hypopg \
        postgresql-plpython3-${DB_VERSION} \
        python3-pip \
        redis-server \
        nginx \
        certbot \
        python3-certbot-nginx
    
    # Install additional tools
    apt-get install -y \
        htop \
        iotop \
        sysstat \
        bc \
        jq \
        curl \
        mailutils
    
    log "INFO" "PostgreSQL installation completed"
}

# Configure PostgreSQL
configure_postgresql() {
    log "INFO" "Configuring PostgreSQL for production..."
    
    local pg_config_dir="/etc/postgresql/${DB_VERSION}/main"
    local pg_data_dir="/var/lib/postgresql/${DB_VERSION}/main"
    
    # Stop PostgreSQL service
    systemctl stop postgresql
    
    # Backup original configuration
    cp "${pg_config_dir}/postgresql.conf" "${pg_config_dir}/postgresql.conf.backup"
    cp "${pg_config_dir}/pg_hba.conf" "${pg_config_dir}/pg_hba.conf.backup"
    
    # Copy production configuration
    cp "$(dirname "$0")/../postgresql.production.conf" "${pg_config_dir}/postgresql.conf"
    
    # Update configuration with system-specific values
    local total_memory=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    local shared_buffers=$((total_memory / 4))
    local effective_cache_size=$((total_memory * 3 / 4))
    local work_mem=$((total_memory / 50))
    
    sed -i "s/shared_buffers = 512MB/shared_buffers = ${shared_buffers}MB/" "${pg_config_dir}/postgresql.conf"
    sed -i "s/effective_cache_size = 1536MB/effective_cache_size = ${effective_cache_size}MB/" "${pg_config_dir}/postgresql.conf"
    sed -i "s/work_mem = 8MB/work_mem = ${work_mem}MB/" "${pg_config_dir}/postgresql.conf"
    
    # Configure pg_hba.conf for security
    cat > "${pg_config_dir}/pg_hba.conf" << EOF
# PostgreSQL Client Authentication Configuration File
# PHCityRent Production Configuration

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             postgres                                peer
local   all             all                                     md5

# IPv4 local connections:
host    all             all             127.0.0.1/32            md5
host    all             all             10.0.0.0/8              md5
host    all             all             **********/12           md5
host    all             all             ***********/16          md5

# IPv6 local connections:
host    all             all             ::1/128                 md5

# Replication connections
host    replication     replicator      10.0.0.0/8              md5
host    replication     replicator      **********/12           md5
host    replication     replicator      ***********/16          md5

# SSL connections (production)
hostssl all             all             0.0.0.0/0               md5
EOF
    
    # Set proper permissions
    chown postgres:postgres "${pg_config_dir}/postgresql.conf"
    chown postgres:postgres "${pg_config_dir}/pg_hba.conf"
    chmod 640 "${pg_config_dir}/postgresql.conf"
    chmod 640 "${pg_config_dir}/pg_hba.conf"
    
    log "INFO" "PostgreSQL configuration completed"
}

# Setup SSL certificates
setup_ssl() {
    log "INFO" "Setting up SSL certificates..."
    
    # Create SSL directory
    mkdir -p "${SSL_CERT_PATH}"
    
    # Generate self-signed certificate for development/testing
    if [[ "${ENVIRONMENT}" != "production" ]]; then
        openssl req -new -x509 -days 365 -nodes -text \
            -out "${SSL_CERT_PATH}/server.crt" \
            -keyout "${SSL_CERT_PATH}/server.key" \
            -subj "/CN=phcityrent-db"
        
        chmod 600 "${SSL_CERT_PATH}/server.key"
        chown postgres:postgres "${SSL_CERT_PATH}/server.key"
        chown postgres:postgres "${SSL_CERT_PATH}/server.crt"
    else
        log "WARNING" "For production, please install proper SSL certificates"
        log "WARNING" "Update postgresql.conf with correct certificate paths"
    fi
    
    log "INFO" "SSL setup completed"
}

# Create database and user
create_database() {
    log "INFO" "Creating database and user..."
    
    # Start PostgreSQL
    systemctl start postgresql
    systemctl enable postgresql
    
    # Wait for PostgreSQL to start
    sleep 5
    
    # Create application database and user
    sudo -u postgres psql << EOF
-- Create application user
CREATE USER ${DB_USER} WITH PASSWORD '${DB_PASSWORD}';

-- Create database
CREATE DATABASE ${DB_NAME} OWNER ${DB_USER};

-- Grant necessary privileges
GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};

-- Connect to the application database
\c ${DB_NAME}

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_buffercache";
CREATE EXTENSION IF NOT EXISTS "pgstattuple";
CREATE EXTENSION IF NOT EXISTS "pg_prewarm";
CREATE EXTENSION IF NOT EXISTS "pg_cron";

-- Grant usage on extensions
GRANT USAGE ON SCHEMA public TO ${DB_USER};
GRANT CREATE ON SCHEMA public TO ${DB_USER};

-- Set up pg_cron permissions
GRANT USAGE ON SCHEMA cron TO ${DB_USER};
EOF
    
    log "INFO" "Database and user created successfully"
}

# Run database migrations
run_migrations() {
    log "INFO" "Running database migrations..."
    
    local migration_dir="$(dirname "$0")/../migrations"
    
    if [[ -d "${migration_dir}" ]]; then
        # Run migrations in order
        for migration_file in "${migration_dir}"/*.sql; do
            if [[ -f "${migration_file}" ]]; then
                log "INFO" "Running migration: $(basename "${migration_file}")"
                sudo -u postgres psql -d "${DB_NAME}" -f "${migration_file}" || {
                    log "ERROR" "Migration failed: $(basename "${migration_file}")"
                    return 1
                }
            fi
        done
        
        log "INFO" "All migrations completed successfully"
    else
        log "WARNING" "Migration directory not found: ${migration_dir}"
    fi
}

# Setup monitoring and backup
setup_monitoring() {
    log "INFO" "Setting up monitoring and backup..."
    
    # Create directories
    mkdir -p "${BACKUP_DIR}"
    mkdir -p "${LOG_DIR}"
    chown postgres:postgres "${BACKUP_DIR}"
    chown postgres:postgres "${LOG_DIR}"
    
    # Copy monitoring scripts
    cp "$(dirname "$0")/db_monitor.sh" "/usr/local/bin/"
    chmod +x "/usr/local/bin/db_monitor.sh"
    
    # Setup cron jobs for monitoring and backup
    sudo -u postgres crontab << EOF
# PHCityRent Database Maintenance
# Monitor database every 5 minutes
*/5 * * * * /usr/local/bin/db_monitor.sh

# Full backup daily at 2 AM
0 2 * * * /usr/local/bin/db_monitor.sh --backup

# Performance report weekly on Sunday at 3 AM
0 3 * * 0 /usr/local/bin/db_monitor.sh --full-report

# Clean up old logs monthly
0 0 1 * * find ${LOG_DIR} -name "*.log" -mtime +30 -delete
EOF
    
    log "INFO" "Monitoring and backup setup completed"
}

# Configure firewall
configure_firewall() {
    log "INFO" "Configuring firewall..."
    
    # Install and configure UFW
    apt-get install -y ufw
    
    # Default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH
    ufw allow ssh
    
    # Allow PostgreSQL (only from private networks)
    ufw allow from 10.0.0.0/8 to any port 5432
    ufw allow from **********/12 to any port 5432
    ufw allow from ***********/16 to any port 5432
    
    # Allow HTTP and HTTPS for web interface
    ufw allow http
    ufw allow https
    
    # Enable firewall
    ufw --force enable
    
    log "INFO" "Firewall configuration completed"
}

# Setup log rotation
setup_log_rotation() {
    log "INFO" "Setting up log rotation..."
    
    cat > "/etc/logrotate.d/phcityrent-db" << EOF
${LOG_DIR}/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 postgres postgres
    postrotate
        systemctl reload postgresql
    endscript
}
EOF
    
    log "INFO" "Log rotation setup completed"
}

# Performance tuning
apply_performance_tuning() {
    log "INFO" "Applying performance tuning..."
    
    # System-level optimizations
    cat >> "/etc/sysctl.conf" << EOF

# PHCityRent Database Optimizations
vm.swappiness = 1
vm.dirty_background_ratio = 5
vm.dirty_ratio = 10
vm.dirty_expire_centisecs = 500
vm.dirty_writeback_centisecs = 100
kernel.shmmax = $(free -b | awk 'NR==2{print $2}')
kernel.shmall = $(free -b | awk 'NR==2{printf "%.0f", $2/4096}')
EOF
    
    # Apply sysctl changes
    sysctl -p
    
    # Run performance tuning SQL
    sudo -u postgres psql -d "${DB_NAME}" -f "$(dirname "$0")/performance_tuning.sql"
    
    log "INFO" "Performance tuning completed"
}

# Verify installation
verify_installation() {
    log "INFO" "Verifying installation..."
    
    # Test database connection
    if sudo -u postgres psql -d "${DB_NAME}" -c "SELECT version();" >/dev/null 2>&1; then
        log "INFO" "✓ Database connection successful"
    else
        error_exit "✗ Database connection failed"
    fi
    
    # Test extensions
    local extensions=("uuid-ossp" "pg_trgm" "pg_stat_statements")
    for ext in "${extensions[@]}"; do
        if sudo -u postgres psql -d "${DB_NAME}" -c "SELECT * FROM pg_extension WHERE extname='${ext}';" | grep -q "${ext}"; then
            log "INFO" "✓ Extension ${ext} installed"
        else
            log "WARNING" "✗ Extension ${ext} not found"
        fi
    done
    
    # Test monitoring script
    if /usr/local/bin/db_monitor.sh >/dev/null 2>&1; then
        log "INFO" "✓ Monitoring script working"
    else
        log "WARNING" "✗ Monitoring script failed"
    fi
    
    log "INFO" "Installation verification completed"
}

# Main deployment function
main() {
    log "INFO" "Starting PHCityRent production database deployment..."
    
    # Create log directory
    mkdir -p "${LOG_DIR}"
    
    check_root
    validate_config
    install_postgresql
    configure_postgresql
    setup_ssl
    create_database
    run_migrations
    setup_monitoring
    configure_firewall
    setup_log_rotation
    apply_performance_tuning
    verify_installation
    
    log "INFO" "🎉 PHCityRent production database deployment completed successfully!"
    log "INFO" "Database: ${DB_NAME}"
    log "INFO" "User: ${DB_USER}"
    log "INFO" "Backup directory: ${BACKUP_DIR}"
    log "INFO" "Log directory: ${LOG_DIR}"
    log "INFO" "Next steps:"
    log "INFO" "1. Update application configuration with database credentials"
    log "INFO" "2. Configure SSL certificates for production"
    log "INFO" "3. Set up read replicas if needed"
    log "INFO" "4. Configure monitoring alerts"
}

# Script usage
usage() {
    echo "PHCityRent Production Database Deployment Script"
    echo "Usage: $0"
    echo
    echo "Required Environment Variables:"
    echo "  DB_PASSWORD      Database password (minimum 12 characters)"
    echo
    echo "Optional Environment Variables:"
    echo "  DB_VERSION       PostgreSQL version (default: 15)"
    echo "  DB_NAME          Database name (default: phcityrent)"
    echo "  DB_USER          Database user (default: phcityrent_user)"
    echo "  ADMIN_EMAIL      Admin email for alerts (default: <EMAIL>)"
    echo "  BACKUP_DIR       Backup directory (default: /var/backups/postgresql)"
    echo "  LOG_DIR          Log directory (default: /var/log/phcityrent)"
    echo "  ENVIRONMENT      Environment (default: production)"
}

# Handle command line arguments
if [[ "${1:-}" == "--help" ]]; then
    usage
    exit 0
fi

main
