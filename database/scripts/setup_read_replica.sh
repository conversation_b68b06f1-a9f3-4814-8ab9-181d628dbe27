#!/bin/bash

# =====================================================
# PHCityRent Read Replica Setup Script
# Automated setup for PostgreSQL streaming replication
# =====================================================

set -euo pipefail

# Configuration
PRIMARY_HOST="${PRIMARY_HOST:-}"
PRIMARY_PORT="${PRIMARY_PORT:-5432}"
REPLICA_HOST="${REPLICA_HOST:-localhost}"
REPLICA_PORT="${REPLICA_PORT:-5433}"
POSTGRES_USER="${POSTGRES_USER:-postgres}"
REPLICATION_USER="${REPLICATION_USER:-replicator}"
REPLICATION_PASSWORD="${REPLICATION_PASSWORD:-}"
POSTGRES_DATA_DIR="${POSTGRES_DATA_DIR:-/var/lib/postgresql/data}"
REPLICA_DATA_DIR="${REPLICA_DATA_DIR:-/var/lib/postgresql/replica}"
BACKUP_DIR="${BACKUP_DIR:-/var/backups/postgresql}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

# Check if running as postgres user
check_user() {
    if [[ $(whoami) != "postgres" ]]; then
        error_exit "This script must be run as the postgres user"
    fi
}

# Validate configuration
validate_config() {
    log "INFO" "Validating configuration..."
    
    if [[ -z "${PRIMARY_HOST}" ]]; then
        error_exit "PRIMARY_HOST must be set"
    fi
    
    if [[ -z "${REPLICATION_PASSWORD}" ]]; then
        error_exit "REPLICATION_PASSWORD must be set"
    fi
    
    log "INFO" "Configuration validated"
}

# Setup replication user on primary
setup_replication_user() {
    log "INFO" "Setting up replication user on primary server..."
    
    # Connect to primary and create replication user
    PGPASSWORD="${REPLICATION_PASSWORD}" psql -h "${PRIMARY_HOST}" -p "${PRIMARY_PORT}" -U "${POSTGRES_USER}" -d postgres -c "
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = '${REPLICATION_USER}') THEN
                CREATE ROLE ${REPLICATION_USER} WITH REPLICATION LOGIN PASSWORD '${REPLICATION_PASSWORD}';
            END IF;
        END
        \$\$;
    " || error_exit "Failed to create replication user"
    
    log "INFO" "Replication user created successfully"
}

# Configure primary server for replication
configure_primary() {
    log "INFO" "Configuring primary server for replication..."
    
    # Update postgresql.conf on primary
    local primary_config_updates="
# Replication settings
wal_level = replica
max_wal_senders = 5
wal_keep_segments = 64
archive_mode = on
archive_command = 'cp %p ${BACKUP_DIR}/wal_archive/%f'
hot_standby = on
"
    
    # Update pg_hba.conf on primary to allow replication connections
    local hba_entry="host replication ${REPLICATION_USER} ${REPLICA_HOST}/32 md5"
    
    log "INFO" "Primary server configuration completed"
    log "WARNING" "Please add the following to postgresql.conf on primary server:"
    echo "${primary_config_updates}"
    log "WARNING" "Please add the following to pg_hba.conf on primary server:"
    echo "${hba_entry}"
    log "WARNING" "Then restart the primary PostgreSQL server"
}

# Create base backup from primary
create_base_backup() {
    log "INFO" "Creating base backup from primary server..."
    
    # Stop replica PostgreSQL if running
    if systemctl is-active --quiet postgresql; then
        log "INFO" "Stopping PostgreSQL service..."
        sudo systemctl stop postgresql
    fi
    
    # Remove existing replica data directory
    if [[ -d "${REPLICA_DATA_DIR}" ]]; then
        log "INFO" "Removing existing replica data directory..."
        rm -rf "${REPLICA_DATA_DIR}"
    fi
    
    # Create replica data directory
    mkdir -p "${REPLICA_DATA_DIR}"
    
    # Create base backup
    log "INFO" "Starting base backup (this may take a while)..."
    PGPASSWORD="${REPLICATION_PASSWORD}" pg_basebackup \
        -h "${PRIMARY_HOST}" \
        -p "${PRIMARY_PORT}" \
        -U "${REPLICATION_USER}" \
        -D "${REPLICA_DATA_DIR}" \
        -P \
        -W \
        -R \
        -X stream \
        --checkpoint=fast || error_exit "Base backup failed"
    
    log "INFO" "Base backup completed successfully"
}

# Configure replica server
configure_replica() {
    log "INFO" "Configuring replica server..."
    
    # Create recovery.conf (for PostgreSQL < 12) or update postgresql.conf (for PostgreSQL >= 12)
    local pg_version=$(psql --version | awk '{print $3}' | sed 's/\..*//')
    
    if [[ ${pg_version} -lt 12 ]]; then
        # PostgreSQL < 12: Create recovery.conf
        cat > "${REPLICA_DATA_DIR}/recovery.conf" << EOF
standby_mode = 'on'
primary_conninfo = 'host=${PRIMARY_HOST} port=${PRIMARY_PORT} user=${REPLICATION_USER} password=${REPLICATION_PASSWORD} application_name=replica1'
trigger_file = '${REPLICA_DATA_DIR}/trigger_file'
restore_command = 'cp ${BACKUP_DIR}/wal_archive/%f %p'
archive_cleanup_command = 'pg_archivecleanup ${BACKUP_DIR}/wal_archive %r'
EOF
    else
        # PostgreSQL >= 12: Update postgresql.conf and create standby.signal
        cat >> "${REPLICA_DATA_DIR}/postgresql.conf" << EOF

# Replica-specific settings
hot_standby = on
max_standby_streaming_delay = 30s
max_standby_archive_delay = 30s
wal_receiver_status_interval = 10s
hot_standby_feedback = on
primary_conninfo = 'host=${PRIMARY_HOST} port=${PRIMARY_PORT} user=${REPLICATION_USER} password=${REPLICATION_PASSWORD} application_name=replica1'
restore_command = 'cp ${BACKUP_DIR}/wal_archive/%f %p'
archive_cleanup_command = 'pg_archivecleanup ${BACKUP_DIR}/wal_archive %r'
EOF
        
        # Create standby.signal file
        touch "${REPLICA_DATA_DIR}/standby.signal"
    fi
    
    # Update port in postgresql.conf for replica
    sed -i "s/#port = 5432/port = ${REPLICA_PORT}/" "${REPLICA_DATA_DIR}/postgresql.conf"
    
    # Set proper permissions
    chmod 700 "${REPLICA_DATA_DIR}"
    
    log "INFO" "Replica server configured successfully"
}

# Start replica server
start_replica() {
    log "INFO" "Starting replica PostgreSQL server..."
    
    # Start PostgreSQL with replica data directory
    pg_ctl -D "${REPLICA_DATA_DIR}" -l "${REPLICA_DATA_DIR}/postgresql.log" start || error_exit "Failed to start replica server"
    
    # Wait for server to start
    sleep 5
    
    # Test replica connection
    if psql -h "${REPLICA_HOST}" -p "${REPLICA_PORT}" -U "${POSTGRES_USER}" -d postgres -c "SELECT pg_is_in_recovery();" | grep -q "t"; then
        log "INFO" "Replica server started successfully and is in recovery mode"
    else
        error_exit "Replica server is not in recovery mode"
    fi
}

# Verify replication
verify_replication() {
    log "INFO" "Verifying replication setup..."
    
    # Check replication status on primary
    log "INFO" "Checking replication status on primary..."
    PGPASSWORD="${REPLICATION_PASSWORD}" psql -h "${PRIMARY_HOST}" -p "${PRIMARY_PORT}" -U "${POSTGRES_USER}" -d postgres -c "
        SELECT client_addr, state, sync_state, sync_priority 
        FROM pg_stat_replication 
        WHERE usename = '${REPLICATION_USER}';
    "
    
    # Check replication lag
    log "INFO" "Checking replication lag..."
    local lag=$(psql -h "${REPLICA_HOST}" -p "${REPLICA_PORT}" -U "${POSTGRES_USER}" -d postgres -t -c "
        SELECT EXTRACT(EPOCH FROM (now() - pg_last_xact_replay_timestamp()));
    " | tr -d ' ')
    
    if [[ -n "${lag}" ]] && [[ "${lag}" != "" ]]; then
        log "INFO" "Replication lag: ${lag} seconds"
        if (( $(echo "${lag} > 60" | bc -l) )); then
            log "WARNING" "High replication lag detected: ${lag} seconds"
        fi
    else
        log "INFO" "No replication lag data available (normal for new replica)"
    fi
    
    # Test data consistency
    log "INFO" "Testing data consistency..."
    local primary_count=$(PGPASSWORD="${REPLICATION_PASSWORD}" psql -h "${PRIMARY_HOST}" -p "${PRIMARY_PORT}" -U "${POSTGRES_USER}" -d phcityrent -t -c "
        SELECT count(*) FROM properties;
    " | tr -d ' ')
    
    local replica_count=$(psql -h "${REPLICA_HOST}" -p "${REPLICA_PORT}" -U "${POSTGRES_USER}" -d phcityrent -t -c "
        SELECT count(*) FROM properties;
    " | tr -d ' ')
    
    if [[ "${primary_count}" == "${replica_count}" ]]; then
        log "INFO" "Data consistency verified: ${primary_count} records on both servers"
    else
        log "WARNING" "Data inconsistency detected: Primary=${primary_count}, Replica=${replica_count}"
    fi
}

# Create monitoring script for replica
create_monitoring_script() {
    log "INFO" "Creating replica monitoring script..."
    
    cat > "/usr/local/bin/monitor_replica.sh" << 'EOF'
#!/bin/bash

# PHCityRent Replica Monitoring Script

REPLICA_HOST="localhost"
REPLICA_PORT="5433"
POSTGRES_USER="postgres"
LOG_FILE="/var/log/postgresql/replica_monitor.log"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') $*" >> "${LOG_FILE}"
}

# Check if replica is running
if ! psql -h "${REPLICA_HOST}" -p "${REPLICA_PORT}" -U "${POSTGRES_USER}" -d postgres -c "SELECT 1;" >/dev/null 2>&1; then
    log "ERROR: Replica server is not responding"
    exit 1
fi

# Check if replica is in recovery mode
if ! psql -h "${REPLICA_HOST}" -p "${REPLICA_PORT}" -U "${POSTGRES_USER}" -d postgres -c "SELECT pg_is_in_recovery();" | grep -q "t"; then
    log "ERROR: Replica is not in recovery mode"
    exit 1
fi

# Check replication lag
LAG=$(psql -h "${REPLICA_HOST}" -p "${REPLICA_PORT}" -U "${POSTGRES_USER}" -d postgres -t -c "
    SELECT EXTRACT(EPOCH FROM (now() - pg_last_xact_replay_timestamp()));
" | tr -d ' ')

if [[ -n "${LAG}" ]] && [[ "${LAG}" != "" ]]; then
    if (( $(echo "${LAG} > 300" | bc -l) )); then
        log "WARNING: High replication lag: ${LAG} seconds"
    else
        log "INFO: Replication lag: ${LAG} seconds"
    fi
fi

log "INFO: Replica monitoring check completed"
EOF
    
    chmod +x "/usr/local/bin/monitor_replica.sh"
    
    # Add to crontab for regular monitoring
    (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/monitor_replica.sh") | crontab -
    
    log "INFO" "Replica monitoring script created and scheduled"
}

# Main setup function
main() {
    log "INFO" "Starting PHCityRent read replica setup..."
    
    check_user
    validate_config
    
    case "${1:-setup}" in
        "setup")
            setup_replication_user
            configure_primary
            create_base_backup
            configure_replica
            start_replica
            verify_replication
            create_monitoring_script
            log "INFO" "Read replica setup completed successfully!"
            ;;
        "verify")
            verify_replication
            ;;
        "monitor")
            /usr/local/bin/monitor_replica.sh
            ;;
        *)
            echo "Usage: $0 [setup|verify|monitor]"
            echo "  setup   - Complete replica setup (default)"
            echo "  verify  - Verify existing replication"
            echo "  monitor - Run monitoring check"
            exit 1
            ;;
    esac
}

# Script usage
usage() {
    echo "PHCityRent Read Replica Setup Script"
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  setup    Complete replica setup (default)"
    echo "  verify   Verify existing replication"
    echo "  monitor  Run monitoring check"
    echo
    echo "Required Environment Variables:"
    echo "  PRIMARY_HOST           Primary server hostname/IP"
    echo "  REPLICATION_PASSWORD   Password for replication user"
    echo
    echo "Optional Environment Variables:"
    echo "  PRIMARY_PORT           Primary server port (default: 5432)"
    echo "  REPLICA_HOST           Replica server hostname (default: localhost)"
    echo "  REPLICA_PORT           Replica server port (default: 5433)"
    echo "  POSTGRES_USER          PostgreSQL superuser (default: postgres)"
    echo "  REPLICATION_USER       Replication user name (default: replicator)"
    echo "  POSTGRES_DATA_DIR      Primary data directory (default: /var/lib/postgresql/data)"
    echo "  REPLICA_DATA_DIR       Replica data directory (default: /var/lib/postgresql/replica)"
    echo "  BACKUP_DIR             Backup directory (default: /var/backups/postgresql)"
}

# Handle command line arguments
if [[ "${1:-}" == "--help" ]]; then
    usage
    exit 0
fi

main "${1:-setup}"
