-- =====================================================
-- PHCityRent Database Performance Tuning
-- Advanced performance optimization queries and procedures
-- =====================================================

-- =====================================================
-- QUERY PERFORMANCE ANALYSIS
-- =====================================================

-- Find slow queries that need optimization
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    ROUND((100.0 * total_time / sum(total_time) OVER())::numeric, 2) AS percentage,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 20;

-- Find queries with low cache hit ratio
SELECT 
    query,
    calls,
    shared_blks_hit,
    shared_blks_read,
    CASE 
        WHEN shared_blks_hit + shared_blks_read = 0 THEN 0
        ELSE ROUND((100.0 * shared_blks_hit / (shared_blks_hit + shared_blks_read))::numeric, 2)
    END AS hit_ratio
FROM pg_stat_statements 
WHERE shared_blks_read > 0
ORDER BY hit_ratio ASC, calls DESC
LIMIT 20;

-- =====================================================
-- INDEX ANALYSIS AND RECOMMENDATIONS
-- =====================================================

-- Find unused indexes (candidates for removal)
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE idx_tup_read = 0 
AND idx_tup_fetch = 0
AND schemaname = 'public'
ORDER BY pg_relation_size(indexrelid) DESC;

-- Find tables that might need indexes (high sequential scans)
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    CASE 
        WHEN seq_scan + idx_scan = 0 THEN 0
        ELSE ROUND((100.0 * seq_scan / (seq_scan + idx_scan))::numeric, 2)
    END AS seq_scan_ratio,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size
FROM pg_stat_user_tables 
WHERE seq_scan > 1000
AND schemaname = 'public'
ORDER BY seq_scan DESC;

-- Find duplicate or redundant indexes
SELECT 
    t.tablename,
    array_agg(t.indexname) as duplicate_indexes,
    t.columns
FROM (
    SELECT 
        tablename,
        indexname,
        string_agg(column_name, ',' ORDER BY ordinal_position) as columns
    FROM (
        SELECT 
            t.relname as tablename,
            i.relname as indexname,
            a.attname as column_name,
            a.attnum as ordinal_position
        FROM pg_class t
        JOIN pg_index ix ON t.oid = ix.indrelid
        JOIN pg_class i ON i.oid = ix.indexrelid
        JOIN pg_attribute a ON t.oid = a.attrelid
        WHERE a.attnum = ANY(ix.indkey)
        AND t.relkind = 'r'
        AND t.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
    ) sub
    GROUP BY tablename, indexname
) t
GROUP BY t.tablename, t.columns
HAVING count(*) > 1;

-- =====================================================
-- TABLE MAINTENANCE ANALYSIS
-- =====================================================

-- Find tables that need VACUUM
SELECT 
    schemaname,
    tablename,
    n_dead_tup,
    n_live_tup,
    ROUND((n_dead_tup::float / GREATEST(n_live_tup + n_dead_tup, 1) * 100)::numeric, 2) as dead_tuple_ratio,
    last_vacuum,
    last_autovacuum,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size
FROM pg_stat_user_tables 
WHERE n_dead_tup > 1000
ORDER BY dead_tuple_ratio DESC;

-- Find tables that need ANALYZE
SELECT 
    schemaname,
    tablename,
    n_mod_since_analyze,
    last_analyze,
    last_autoanalyze,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size
FROM pg_stat_user_tables 
WHERE n_mod_since_analyze > 1000
ORDER BY n_mod_since_analyze DESC;

-- =====================================================
-- CONNECTION AND LOCK ANALYSIS
-- =====================================================

-- Current active connections and their states
SELECT 
    state,
    count(*) as connection_count,
    ROUND(AVG(EXTRACT(EPOCH FROM (now() - state_change)))::numeric, 2) as avg_duration_seconds
FROM pg_stat_activity 
WHERE state IS NOT NULL
GROUP BY state
ORDER BY connection_count DESC;

-- Long running queries
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query,
    state,
    client_addr
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
AND state != 'idle'
ORDER BY duration DESC;

-- Current locks and blocking queries
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process,
    blocked_activity.application_name AS blocked_application,
    blocking_activity.application_name AS blocking_application
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks 
    ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.DATABASE IS NOT DISTINCT FROM blocked_locks.DATABASE
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.GRANTED;

-- =====================================================
-- STORAGE AND BLOAT ANALYSIS
-- =====================================================

-- Database size breakdown
SELECT 
    datname as database_name,
    pg_size_pretty(pg_database_size(datname)) as database_size,
    ROUND((pg_database_size(datname)::float / (SELECT sum(pg_database_size(datname)) FROM pg_database) * 100)::numeric, 2) as percentage
FROM pg_database 
WHERE datistemplate = false
ORDER BY pg_database_size(datname) DESC;

-- Table size analysis
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size,
    ROUND((pg_total_relation_size(schemaname||'.'||tablename)::float / 
           (SELECT sum(pg_total_relation_size(schemaname||'.'||tablename)) 
            FROM pg_tables WHERE schemaname = 'public') * 100)::numeric, 2) as percentage
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Index usage efficiency
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    CASE 
        WHEN idx_tup_read = 0 THEN 0
        ELSE ROUND((idx_tup_fetch::float / idx_tup_read * 100)::numeric, 2)
    END as efficiency_ratio,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
AND idx_tup_read > 0
ORDER BY efficiency_ratio DESC;

-- =====================================================
-- PERFORMANCE OPTIMIZATION PROCEDURES
-- =====================================================

-- Procedure to optimize a specific table
CREATE OR REPLACE FUNCTION optimize_table(
    p_schema_name TEXT,
    p_table_name TEXT,
    p_analyze_only BOOLEAN DEFAULT false
)
RETURNS TEXT AS $$
DECLARE
    optimization_log TEXT := '';
    table_size BIGINT;
    dead_tuple_ratio NUMERIC;
BEGIN
    -- Get table statistics
    SELECT 
        pg_total_relation_size(p_schema_name||'.'||p_table_name),
        ROUND((n_dead_tup::float / GREATEST(n_live_tup + n_dead_tup, 1) * 100)::numeric, 2)
    INTO table_size, dead_tuple_ratio
    FROM pg_stat_user_tables 
    WHERE schemaname = p_schema_name AND tablename = p_table_name;
    
    optimization_log := optimization_log || 'Table: ' || p_schema_name || '.' || p_table_name || E'\n';
    optimization_log := optimization_log || 'Size: ' || pg_size_pretty(table_size) || E'\n';
    optimization_log := optimization_log || 'Dead tuple ratio: ' || dead_tuple_ratio || '%' || E'\n';
    
    IF NOT p_analyze_only THEN
        -- Perform VACUUM if needed
        IF dead_tuple_ratio > 20 THEN
            EXECUTE format('VACUUM ANALYZE %I.%I', p_schema_name, p_table_name);
            optimization_log := optimization_log || 'Performed: VACUUM ANALYZE' || E'\n';
        ELSE
            EXECUTE format('ANALYZE %I.%I', p_schema_name, p_table_name);
            optimization_log := optimization_log || 'Performed: ANALYZE' || E'\n';
        END IF;
        
        -- Reindex if table is large and has high dead tuple ratio
        IF table_size > 100 * 1024 * 1024 AND dead_tuple_ratio > 30 THEN -- 100MB
            EXECUTE format('REINDEX TABLE %I.%I', p_schema_name, p_table_name);
            optimization_log := optimization_log || 'Performed: REINDEX' || E'\n';
        END IF;
    END IF;
    
    RETURN optimization_log;
END;
$$ LANGUAGE plpgsql;

-- Procedure to generate index recommendations
CREATE OR REPLACE FUNCTION generate_index_recommendations()
RETURNS TABLE(
    table_name TEXT,
    recommendation TEXT,
    reason TEXT,
    estimated_benefit TEXT
) AS $$
BEGIN
    -- Tables with high sequential scan ratio
    RETURN QUERY
    SELECT 
        t.tablename::TEXT,
        'Consider adding indexes for frequently scanned columns'::TEXT,
        'High sequential scan ratio: ' || 
        ROUND((100.0 * t.seq_scan / GREATEST(t.seq_scan + t.idx_scan, 1))::numeric, 2)::TEXT || '%',
        'Potential query performance improvement'::TEXT
    FROM pg_stat_user_tables t
    WHERE t.seq_scan > 1000
    AND (100.0 * t.seq_scan / GREATEST(t.seq_scan + t.idx_scan, 1)) > 80
    AND t.schemaname = 'public';
    
    -- Unused indexes
    RETURN QUERY
    SELECT 
        i.tablename::TEXT,
        'Consider dropping unused index: ' || i.indexname,
        'Index has zero reads and fetches'::TEXT,
        'Storage space savings: ' || pg_size_pretty(pg_relation_size(i.indexrelid))
    FROM pg_stat_user_indexes i
    WHERE i.idx_tup_read = 0 
    AND i.idx_tup_fetch = 0
    AND i.schemaname = 'public';
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION optimize_table(TEXT, TEXT, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION generate_index_recommendations() TO authenticated;
