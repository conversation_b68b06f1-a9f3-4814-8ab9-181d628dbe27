#!/bin/bash

# =====================================================
# PHCityRent Database Monitoring and Maintenance Script
# Production-grade database monitoring and automated maintenance
# =====================================================

set -euo pipefail

# Configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-phcityrent}"
DB_USER="${DB_USER:-postgres}"
BACKUP_DIR="${BACKUP_DIR:-/var/backups/postgresql}"
LOG_DIR="${LOG_DIR:-/var/log/phcityrent}"
ALERT_EMAIL="${ALERT_EMAIL:-<EMAIL>}"
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"

# Thresholds
MAX_CONNECTIONS_THRESHOLD=80
CACHE_HIT_RATIO_THRESHOLD=95
DISK_USAGE_THRESHOLD=85
SLOW_QUERY_THRESHOLD=5000
REPLICATION_LAG_THRESHOLD=60

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_DIR}/db_monitor.log"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    send_alert "ERROR" "$1"
    exit 1
}

# Send alerts via email and Slack
send_alert() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Email alert
    if command -v mail >/dev/null 2>&1; then
        echo "PHCityRent Database Alert - ${level}
        
Time: ${timestamp}
Message: ${message}
Host: ${DB_HOST}
Database: ${DB_NAME}

Please investigate immediately." | mail -s "PHCityRent DB Alert: ${level}" "${ALERT_EMAIL}"
    fi
    
    # Slack alert
    if [[ -n "${SLACK_WEBHOOK}" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 PHCityRent DB Alert: ${level}\\n${message}\\nHost: ${DB_HOST}\\nTime: ${timestamp}\"}" \
            "${SLACK_WEBHOOK}" >/dev/null 2>&1 || true
    fi
}

# Database connection test
test_connection() {
    log "INFO" "Testing database connection..."
    if ! psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "SELECT 1;" >/dev/null 2>&1; then
        error_exit "Cannot connect to database ${DB_NAME} on ${DB_HOST}:${DB_PORT}"
    fi
    log "INFO" "Database connection successful"
}

# Monitor connection count
monitor_connections() {
    log "INFO" "Monitoring database connections..."
    
    local active_connections=$(psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c "
        SELECT count(*) FROM pg_stat_activity WHERE state = 'active';
    " | tr -d ' ')
    
    local max_connections=$(psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c "
        SHOW max_connections;
    " | tr -d ' ')
    
    local connection_percentage=$((active_connections * 100 / max_connections))
    
    log "INFO" "Active connections: ${active_connections}/${max_connections} (${connection_percentage}%)"
    
    if [[ ${connection_percentage} -gt ${MAX_CONNECTIONS_THRESHOLD} ]]; then
        send_alert "WARNING" "High connection usage: ${connection_percentage}% (${active_connections}/${max_connections})"
    fi
}

# Monitor cache hit ratio
monitor_cache_hit_ratio() {
    log "INFO" "Monitoring cache hit ratio..."
    
    local cache_hit_ratio=$(psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c "
        SELECT ROUND(
            (sum(heap_blks_hit) / GREATEST(sum(heap_blks_hit + heap_blks_read), 1)) * 100, 2
        ) FROM pg_stat_user_tables;
    " | tr -d ' ')
    
    log "INFO" "Cache hit ratio: ${cache_hit_ratio}%"
    
    if (( $(echo "${cache_hit_ratio} < ${CACHE_HIT_RATIO_THRESHOLD}" | bc -l) )); then
        send_alert "WARNING" "Low cache hit ratio: ${cache_hit_ratio}% (threshold: ${CACHE_HIT_RATIO_THRESHOLD}%)"
    fi
}

# Monitor disk usage
monitor_disk_usage() {
    log "INFO" "Monitoring disk usage..."
    
    local db_size=$(psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c "
        SELECT pg_size_pretty(pg_database_size('${DB_NAME}'));
    " | tr -d ' ')
    
    local disk_usage=$(df -h /var/lib/postgresql | awk 'NR==2 {print $5}' | sed 's/%//')
    
    log "INFO" "Database size: ${db_size}, Disk usage: ${disk_usage}%"
    
    if [[ ${disk_usage} -gt ${DISK_USAGE_THRESHOLD} ]]; then
        send_alert "CRITICAL" "High disk usage: ${disk_usage}% (threshold: ${DISK_USAGE_THRESHOLD}%)"
    fi
}

# Monitor slow queries
monitor_slow_queries() {
    log "INFO" "Monitoring slow queries..."
    
    local slow_queries=$(psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c "
        SELECT count(*) FROM pg_stat_statements 
        WHERE mean_time > ${SLOW_QUERY_THRESHOLD};
    " | tr -d ' ')
    
    log "INFO" "Slow queries (>${SLOW_QUERY_THRESHOLD}ms): ${slow_queries}"
    
    if [[ ${slow_queries} -gt 10 ]]; then
        # Get top 5 slow queries
        local top_slow_queries=$(psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c "
            SELECT query, ROUND(mean_time::numeric, 2) as avg_time_ms, calls
            FROM pg_stat_statements 
            WHERE mean_time > ${SLOW_QUERY_THRESHOLD}
            ORDER BY mean_time DESC 
            LIMIT 5;
        ")
        
        send_alert "WARNING" "High number of slow queries: ${slow_queries}. Top queries: ${top_slow_queries}"
    fi
}

# Monitor replication lag
monitor_replication_lag() {
    log "INFO" "Monitoring replication lag..."
    
    # Check if this is a primary server
    local is_primary=$(psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c "
        SELECT NOT pg_is_in_recovery();
    " | tr -d ' ')
    
    if [[ "${is_primary}" == "t" ]]; then
        # Check replication lag for each replica
        local replicas=$(psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c "
            SELECT client_addr, 
                   EXTRACT(EPOCH FROM (now() - backend_start))::int as connection_age,
                   pg_wal_lsn_diff(pg_current_wal_lsn(), flush_lsn) as lag_bytes
            FROM pg_stat_replication;
        ")
        
        if [[ -n "${replicas}" ]]; then
            log "INFO" "Replication status: ${replicas}"
            
            # Check for high lag (simplified check)
            local high_lag=$(echo "${replicas}" | awk '{if($3 > 1048576) print $1}') # 1MB lag
            if [[ -n "${high_lag}" ]]; then
                send_alert "WARNING" "High replication lag detected for replicas: ${high_lag}"
            fi
        else
            log "INFO" "No active replicas found"
        fi
    else
        log "INFO" "This is a replica server"
    fi
}

# Check table bloat
check_table_bloat() {
    log "INFO" "Checking table bloat..."
    
    local bloated_tables=$(psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -t -c "
        SELECT schemaname, tablename, 
               ROUND(((n_dead_tup::float / GREATEST(n_live_tup + n_dead_tup, 1)) * 100)::numeric, 2) as bloat_percentage
        FROM pg_stat_user_tables 
        WHERE n_dead_tup > 1000 
        AND ((n_dead_tup::float / GREATEST(n_live_tup + n_dead_tup, 1)) * 100) > 20
        ORDER BY bloat_percentage DESC;
    ")
    
    if [[ -n "${bloated_tables}" ]]; then
        log "WARNING" "Bloated tables detected: ${bloated_tables}"
        send_alert "WARNING" "Table bloat detected. Consider running VACUUM ANALYZE on affected tables."
    else
        log "INFO" "No significant table bloat detected"
    fi
}

# Perform database backup
perform_backup() {
    log "INFO" "Starting database backup..."
    
    local backup_timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="${BACKUP_DIR}/phcityrent_backup_${backup_timestamp}.sql"
    
    # Create backup directory if it doesn't exist
    mkdir -p "${BACKUP_DIR}"
    
    # Perform backup
    if pg_dump -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" \
        --verbose --clean --no-owner --no-privileges \
        --format=custom --compress=9 \
        --file="${backup_file}.custom"; then
        
        log "INFO" "Database backup completed: ${backup_file}.custom"
        
        # Also create a plain SQL backup for easier restoration
        pg_dump -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" \
            --verbose --clean --no-owner --no-privileges \
            --format=plain > "${backup_file}"
        
        # Compress the plain backup
        gzip "${backup_file}"
        
        # Remove old backups (keep last 7 days)
        find "${BACKUP_DIR}" -name "phcityrent_backup_*.sql*" -mtime +7 -delete
        
        log "INFO" "Backup cleanup completed"
        
    else
        error_exit "Database backup failed"
    fi
}

# Generate performance report
generate_performance_report() {
    log "INFO" "Generating performance report..."
    
    local report_file="${LOG_DIR}/performance_report_$(date '+%Y%m%d_%H%M%S').txt"
    
    {
        echo "PHCityRent Database Performance Report"
        echo "Generated: $(date)"
        echo "========================================"
        echo
        
        echo "Database Size:"
        psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "
            SELECT pg_size_pretty(pg_database_size('${DB_NAME}')) as database_size;
        "
        echo
        
        echo "Top 10 Largest Tables:"
        psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "
            SELECT schemaname, tablename, 
                   pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
            FROM pg_tables 
            WHERE schemaname = 'public'
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC 
            LIMIT 10;
        "
        echo
        
        echo "Top 10 Most Active Tables:"
        psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "
            SELECT schemaname, tablename, 
                   n_tup_ins + n_tup_upd + n_tup_del as total_activity,
                   n_tup_ins, n_tup_upd, n_tup_del
            FROM pg_stat_user_tables 
            ORDER BY total_activity DESC 
            LIMIT 10;
        "
        echo
        
        echo "Top 10 Slowest Queries:"
        psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "
            SELECT ROUND(mean_time::numeric, 2) as avg_time_ms, 
                   calls, 
                   ROUND((total_time/1000)::numeric, 2) as total_time_sec,
                   LEFT(query, 100) as query_preview
            FROM pg_stat_statements 
            ORDER BY mean_time DESC 
            LIMIT 10;
        "
        
    } > "${report_file}"
    
    log "INFO" "Performance report generated: ${report_file}"
}

# Main monitoring function
main() {
    log "INFO" "Starting PHCityRent database monitoring..."
    
    # Create log directory if it doesn't exist
    mkdir -p "${LOG_DIR}"
    
    # Test database connection
    test_connection
    
    # Run monitoring checks
    monitor_connections
    monitor_cache_hit_ratio
    monitor_disk_usage
    monitor_slow_queries
    monitor_replication_lag
    check_table_bloat
    
    # Generate performance report (daily)
    if [[ "${1:-}" == "--full-report" ]]; then
        generate_performance_report
    fi
    
    # Perform backup (if requested)
    if [[ "${1:-}" == "--backup" ]]; then
        perform_backup
    fi
    
    log "INFO" "Database monitoring completed successfully"
}

# Script usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  --full-report    Generate detailed performance report"
    echo "  --backup         Perform database backup"
    echo "  --help           Show this help message"
    echo
    echo "Environment variables:"
    echo "  DB_HOST          Database host (default: localhost)"
    echo "  DB_PORT          Database port (default: 5432)"
    echo "  DB_NAME          Database name (default: phcityrent)"
    echo "  DB_USER          Database user (default: postgres)"
    echo "  BACKUP_DIR       Backup directory (default: /var/backups/postgresql)"
    echo "  LOG_DIR          Log directory (default: /var/log/phcityrent)"
    echo "  ALERT_EMAIL      Email for alerts (default: <EMAIL>)"
    echo "  SLACK_WEBHOOK    Slack webhook URL for alerts"
}

# Handle command line arguments
case "${1:-}" in
    --help)
        usage
        exit 0
        ;;
    --full-report|--backup)
        main "$1"
        ;;
    "")
        main
        ;;
    *)
        echo "Unknown option: $1"
        usage
        exit 1
        ;;
esac
