# =====================================================
# PRODUCTION POSTGRESQL CONFIGURATION
# Optimized for PHCityRent enterprise deployment
# =====================================================

# =====================================================
# MEMORY SETTINGS
# =====================================================

# Shared memory settings (adjust based on available RAM)
shared_buffers = 512MB                    # 25% of total RAM (for 2GB server)
effective_cache_size = 1536MB             # 75% of total RAM
work_mem = 8MB                            # Per connection working memory
maintenance_work_mem = 128MB              # For maintenance operations
autovacuum_work_mem = 128MB               # For autovacuum processes

# Temporary file settings
temp_buffers = 32MB                       # Temporary table buffer
max_stack_depth = 7MB                     # Maximum stack depth

# =====================================================
# WRITE-AHEAD LOGGING (WAL) SETTINGS
# =====================================================

# WAL configuration for high availability
wal_level = replica                       # Enable streaming replication
wal_buffers = 32MB                        # WAL buffer size
checkpoint_completion_target = 0.9        # Spread checkpoints over time
checkpoint_timeout = 15min                # Maximum time between checkpoints
max_wal_size = 2GB                        # Maximum WAL size before checkpoint
min_wal_size = 512MB                      # Minimum WAL size to keep

# WAL archiving for backup
archive_mode = on                         # Enable WAL archiving
archive_command = 'cp %p /var/lib/postgresql/wal_archive/%f'
archive_timeout = 300                     # Force WAL switch every 5 minutes

# =====================================================
# REPLICATION SETTINGS
# =====================================================

# Streaming replication
max_wal_senders = 5                       # Maximum number of WAL senders
wal_keep_segments = 64                    # Keep WAL segments for replicas
hot_standby = on                          # Enable read-only queries on standby
hot_standby_feedback = on                 # Send feedback to primary

# Synchronous replication (for critical data)
synchronous_standby_names = 'replica1'    # Synchronous replica names
synchronous_commit = on                   # Wait for WAL to be written

# =====================================================
# QUERY PLANNER SETTINGS
# =====================================================

# Cost-based optimizer settings
random_page_cost = 1.1                   # Cost of random page access (SSD optimized)
seq_page_cost = 1.0                      # Cost of sequential page access
cpu_tuple_cost = 0.01                    # Cost of processing each tuple
cpu_index_tuple_cost = 0.005             # Cost of processing index tuple
cpu_operator_cost = 0.0025               # Cost of processing each operator

# Query planning
default_statistics_target = 100          # Statistics target for ANALYZE
constraint_exclusion = partition         # Enable constraint exclusion for partitions
enable_partitionwise_join = on           # Enable partition-wise joins
enable_partitionwise_aggregate = on      # Enable partition-wise aggregates

# Parallel query settings
max_parallel_workers_per_gather = 4      # Parallel workers per query
max_parallel_workers = 8                 # Total parallel workers
max_parallel_maintenance_workers = 4     # Parallel maintenance workers
parallel_tuple_cost = 0.1                # Cost of transferring tuple to parallel worker
parallel_setup_cost = 1000               # Cost of setting up parallel query

# =====================================================
# CONNECTION SETTINGS
# =====================================================

# Connection limits
max_connections = 200                     # Maximum concurrent connections
superuser_reserved_connections = 3       # Reserved connections for superusers

# Connection pooling (if using pgbouncer)
# These settings work well with connection pooling
shared_preload_libraries = 'pg_stat_statements,pg_prewarm,auto_explain'

# Authentication and security
ssl = on                                  # Enable SSL connections
ssl_cert_file = '/etc/ssl/certs/server.crt'
ssl_key_file = '/etc/ssl/private/server.key'
ssl_ca_file = '/etc/ssl/certs/ca.crt'
password_encryption = scram-sha-256       # Use SCRAM-SHA-256 for passwords

# =====================================================
# LOGGING SETTINGS
# =====================================================

# Logging configuration
logging_collector = on                    # Enable log collection
log_directory = '/var/log/postgresql'     # Log directory
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d                     # Rotate logs daily
log_rotation_size = 100MB                 # Rotate when log reaches 100MB
log_truncate_on_rotation = on             # Truncate existing log files

# What to log
log_min_duration_statement = 1000         # Log queries taking > 1 second
log_checkpoints = on                      # Log checkpoint activity
log_connections = on                      # Log new connections
log_disconnections = on                   # Log disconnections
log_lock_waits = on                       # Log lock waits
log_temp_files = 10MB                     # Log temp files > 10MB
log_autovacuum_min_duration = 0           # Log all autovacuum activity

# Log format
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_statement = 'ddl'                     # Log DDL statements
log_min_error_statement = error           # Log statements causing errors

# =====================================================
# AUTOVACUUM SETTINGS
# =====================================================

# Autovacuum configuration
autovacuum = on                           # Enable autovacuum
autovacuum_max_workers = 4                # Maximum autovacuum workers
autovacuum_naptime = 30s                  # Time between autovacuum runs

# Autovacuum thresholds
autovacuum_vacuum_threshold = 50          # Minimum number of updated/deleted tuples
autovacuum_vacuum_scale_factor = 0.1      # Fraction of table size to add to threshold
autovacuum_analyze_threshold = 50         # Minimum number of inserted/updated/deleted tuples
autovacuum_analyze_scale_factor = 0.05    # Fraction of table size to add to threshold

# Autovacuum cost-based delay
autovacuum_vacuum_cost_delay = 10ms       # Delay between vacuum operations
autovacuum_vacuum_cost_limit = 2000       # Cost limit for vacuum operations

# =====================================================
# PERFORMANCE MONITORING
# =====================================================

# Statistics collection
track_activities = on                     # Track running commands
track_counts = on                         # Track table/index access statistics
track_io_timing = on                      # Track I/O timing statistics
track_functions = all                     # Track function call statistics

# pg_stat_statements configuration
pg_stat_statements.max = 10000            # Maximum number of statements tracked
pg_stat_statements.track = all            # Track all statements
pg_stat_statements.track_utility = on     # Track utility statements
pg_stat_statements.save = on              # Save statistics across restarts

# auto_explain configuration
auto_explain.log_min_duration = 5000      # Log plans for queries > 5 seconds
auto_explain.log_analyze = on             # Include actual run times
auto_explain.log_buffers = on             # Include buffer usage statistics
auto_explain.log_timing = on              # Include timing information
auto_explain.log_triggers = on            # Include trigger statistics
auto_explain.log_verbose = on             # Use EXPLAIN VERBOSE

# =====================================================
# BACKGROUND WRITER SETTINGS
# =====================================================

# Background writer configuration
bgwriter_delay = 200ms                    # Delay between background writer rounds
bgwriter_lru_maxpages = 100               # Maximum pages to write per round
bgwriter_lru_multiplier = 2.0             # Multiplier for pages to write
bgwriter_flush_after = 512kB              # Force OS flush after this amount

# =====================================================
# LOCK MANAGEMENT
# =====================================================

# Lock settings
max_locks_per_transaction = 64            # Maximum locks per transaction
max_pred_locks_per_transaction = 64       # Maximum predicate locks per transaction
deadlock_timeout = 1s                     # Time to wait before checking for deadlock

# =====================================================
# ERROR HANDLING
# =====================================================

# Error handling
exit_on_error = off                       # Don't exit on error
restart_after_crash = on                  # Restart after crash
max_files_per_process = 1000              # Maximum open files per process

# =====================================================
# LOCALE AND FORMATTING
# =====================================================

# Locale settings
datestyle = 'iso, mdy'                    # Date style
timezone = 'UTC'                          # Default timezone
lc_messages = 'en_US.UTF-8'               # Language for messages
lc_monetary = 'en_US.UTF-8'               # Locale for monetary formatting
lc_numeric = 'en_US.UTF-8'                # Locale for number formatting
lc_time = 'en_US.UTF-8'                   # Locale for time formatting

# Default text search configuration
default_text_search_config = 'pg_catalog.english'

# =====================================================
# CUSTOM SETTINGS FOR PHCITYRENT
# =====================================================

# Application-specific settings
application_name = 'PHCityRent'           # Application name for monitoring
cluster_name = 'phcityrent-production'    # Cluster name

# Custom variables for application
custom_variable_classes = 'phcityrent'
phcityrent.max_search_results = 1000      # Maximum search results
phcityrent.cache_timeout = 300            # Cache timeout in seconds
phcityrent.enable_analytics = on          # Enable analytics collection

# =====================================================
# RESOURCE LIMITS
# =====================================================

# Resource usage limits
max_worker_processes = 16                 # Maximum background processes
max_logical_replication_workers = 4       # Maximum logical replication workers
max_sync_workers_per_subscription = 2     # Maximum sync workers per subscription

# Statement timeout (prevent runaway queries)
statement_timeout = 300000                # 5 minutes timeout for statements
lock_timeout = 30000                      # 30 seconds timeout for locks
idle_in_transaction_session_timeout = 600000  # 10 minutes timeout for idle transactions

# =====================================================
# EXTENSIONS
# =====================================================

# Shared preload libraries
shared_preload_libraries = 'pg_stat_statements,pg_prewarm,auto_explain,pg_cron'

# Extension-specific settings
pg_prewarm.autoprewarm = on               # Enable automatic prewarming
pg_prewarm.autoprewarm_interval = 300     # Prewarm interval in seconds
