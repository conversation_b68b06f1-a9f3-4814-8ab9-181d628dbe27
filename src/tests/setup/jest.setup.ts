// =====================================================
// JEST SETUP
// Global test setup and configuration
// =====================================================

import { JSDOM } from 'jsdom';
import 'whatwg-fetch';
import '@testing-library/jest-dom';
import 'jest-canvas-mock';
import { configure } from '@testing-library/react';
import { server } from '../mocks/server';
import { cleanup } from '@testing-library/react';

// Set up JSDOM
const dom = new JSDOM('<!doctype html><html><body></body></html>', {
  url: 'http://localhost',
  pretendToBeVisual: true,
  runScripts: 'dangerously'
});

// Set up global variables
global.window = dom.window;
global.document = dom.window.document;
global.navigator = dom.window.navigator;

// Create storage mock
class StorageMock {
  private store: { [key: string]: string } = {};

  clear() {
    this.store = {};
  }

  getItem(key: string) {
    return this.store[key] || null;
  }

  setItem(key: string, value: string) {
    this.store[key] = String(value);
  }

  removeItem(key: string) {
    delete this.store[key];
  }

  get length() {
    return Object.keys(this.store).length;
  }

  key(index: number) {
    const keys = Object.keys(this.store);
    return keys[index] || null;
  }
}

// Set up storage
global.localStorage = new StorageMock();
global.sessionStorage = new StorageMock();

// Set up MSW
beforeAll(() => server.listen({ onUnhandledRequest: 'error' }));
afterEach(() => {
  server.resetHandlers();
  cleanup();
});
afterAll(() => server.close());

// Configure testing library
configure({
  testIdAttribute: 'data-testid',
  });

  // Mock window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });

  // Mock window.scrollTo
  window.scrollTo = jest.fn();

// Mock window.ResizeObserver
window.ResizeObserver = class ResizeObserver {
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
};

// Mock window.IntersectionObserver
window.IntersectionObserver = class IntersectionObserver {
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
  root = null;
  rootMargin = '';
  thresholds = [];
  takeRecords = () => [];
  constructor(callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {}
};

// Test environment variables
process.env.NODE_ENV = 'test';
process.env.VITE_SUPABASE_URL = 'http://localhost:54321';
process.env.VITE_SUPABASE_ANON_KEY = 'test-anon-key';
process.env.VITE_API_URL = 'http://localhost:3000';
process.env.VITE_ENABLE_MOCKS = 'true';
process.env.VITE_MOCK_AUTH = 'true';

// Custom matchers
expect.extend({
  toBeWithinRange(received: number, floor: number, ceiling: number) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },
});

// Global test utilities
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(className: string): R;
      toHaveStyle(style: Record<string, any>): R;
      toBeVisible(): R;
      toBeDisabled(): R;
      toHaveValue(value: string | number): R;
      toHaveTextContent(text: string): R;
    }
  }
}

// File mocks
global.File = class MockFile {
  constructor(
    public parts: (string | Blob | ArrayBuffer | ArrayBufferView)[],
    public name: string,
    public options: FilePropertyBag = {}
  ) {}
  
  get size() { return 1024; }
  get type() { return this.options.type || 'text/plain'; }
  get lastModified() { return this.options.lastModified || Date.now(); }
  
  arrayBuffer() { return Promise.resolve(new ArrayBuffer(1024)); }
  text() { return Promise.resolve('mock file content'); }
  stream() { return new ReadableStream(); }
  slice() { return new Blob(); }
};
