// =====================================================
// AUTH HOOK UNIT TESTS
// Comprehensive tests for authentication hook
// =====================================================

import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuth, AuthProvider } from '@/hooks/useAuth';
import { mockUser, createTestQueryClient } from '@tests/setup/test-utils';
import { server } from '@tests/mocks/server';
import { rest } from 'msw';

// Mock Supabase client
jest.mock('@/integrations/supabase/client', () => {
  const mockUnsubscribe = jest.fn();
  return {
    supabase: {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockImplementation((callback) => {
          callback('INITIAL_SESSION', null);
          return { unsubscribe: mockUnsubscribe };
        }),
        signInWithPassword: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        signUp: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        signOut: jest.fn().mockResolvedValue({ error: null })
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        in: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: null, error: null })
      }))
    }
  };
});

// Test wrapper component
const createWrapper = () => {
  const queryClient = createTestQueryClient();
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {children}
      </AuthProvider>
    </QueryClientProvider>
  );
};

describe('useAuth Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();
  });

  describe('Initial State', () => {
    it('should initialize with no user when not authenticated', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.user).toBeNull();
        expect(result.current.loading).toBe(false);
      });
    });

    it('should initialize with user when authenticated', async () => {
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: mockUser.role
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValueOnce({ data: { session: mockSession }, error: null });

      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'onAuthStateChange')
        .mockImplementationOnce((callback) => {
          setTimeout(() => {
            callback('SIGNED_IN', mockSession);
          }, 0);
          return { unsubscribe: jest.fn() };
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.user).toBeTruthy();
        expect(result.current.user?.id).toBe(mockUser.id);
        expect(result.current.loading).toBe(false);
      });
    });
  });

  describe('Login Functionality', () => {
    it('should login successfully with valid credentials', async () => {
      const mockSession = {
        user: {
          id: mockUser.id,
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'Test User',
            role: 'user'
          }
        }
      };

      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'signInWithPassword')
        .mockResolvedValueOnce({ data: { session: mockSession }, error: null });

      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'onAuthStateChange')
        .mockImplementationOnce((callback) => {
          setTimeout(() => {
            callback('SIGNED_IN', mockSession);
          }, 0);
          return { unsubscribe: jest.fn() };
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      let response;
      await act(async () => {
        response = await result.current.signIn('<EMAIL>', 'password123');
      });

      expect(response.error).toBeUndefined();

      await waitFor(() => {
        expect(result.current.user).toBeTruthy();
        expect(result.current.user?.email).toBe('<EMAIL>');
        expect(result.current.loading).toBe(false);
      });
    });

    it('should handle login failure with invalid credentials', async () => {
      const mockError = { message: 'Invalid credentials' };
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'signInWithPassword')
        .mockResolvedValueOnce({ data: { session: null }, error: mockError });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      let response;
      await act(async () => {
        response = await result.current.signIn('<EMAIL>', 'wrongpassword');
      });

      expect(response.error).toBe(mockError);
      expect(result.current.user).toBeNull();
    });
  });

  describe('Registration Functionality', () => {
    it('should register successfully with valid data', async () => {
      const mockSession = {
        user: {
          id: mockUser.id,
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'New User',
            role: 'user'
          }
        }
      };

      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'signUp')
        .mockResolvedValueOnce({ data: { session: mockSession }, error: null });

      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'onAuthStateChange')
        .mockImplementationOnce((callback) => {
          setTimeout(() => {
            callback('SIGNED_IN', mockSession);
          }, 0);
          return { unsubscribe: jest.fn() };
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      let response;
      await act(async () => {
        response = await result.current.signUp('<EMAIL>', 'password123', 'New User');
      });

      expect(response.error).toBeUndefined();

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });
    });

    it('should handle registration failure', async () => {
      const mockError = { message: 'Email already exists' };
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'signUp')
        .mockResolvedValueOnce({ data: { session: null }, error: mockError });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      let response;
      await act(async () => {
        response = await result.current.signUp('<EMAIL>', 'password123', 'Existing User');
      });

      expect(response.error).toBe(mockError);
      expect(result.current.user).toBeNull();
    });
  });

  describe('Logout Functionality', () => {
    it('should logout successfully', async () => {
      // Start with authenticated session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: mockUser.role
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValueOnce({ data: { session: mockSession }, error: null });

      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'onAuthStateChange')
        .mockImplementationOnce((callback) => {
          setTimeout(() => {
            callback('SIGNED_IN', mockSession);
          }, 0);
          return { unsubscribe: jest.fn() };
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      // Wait for initial auth state
      await waitFor(() => {
        expect(result.current.user).toBeTruthy();
      });

      // Mock signOut
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'signOut')
        .mockResolvedValueOnce({ error: null });

      // Mock auth state change for sign out
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'onAuthStateChange')
        .mockImplementationOnce((callback) => {
          setTimeout(() => {
            callback('SIGNED_OUT', null);
          }, 0);
          return { unsubscribe: jest.fn() };
        });

      await act(async () => {
        await result.current.signOut();
      });

      await waitFor(() => {
        expect(result.current.user).toBeNull();
      });
    });
  });

  describe('Admin Status', () => {
    it('should correctly identify admin users', async () => {
      // Start with authenticated admin session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: 'admin'
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValueOnce({ data: { session: mockSession }, error: null });

      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'onAuthStateChange')
        .mockImplementationOnce((callback) => {
          setTimeout(() => {
            callback('SIGNED_IN', mockSession);
          }, 0);
          return { unsubscribe: jest.fn() };
        });

      // Mock admin role check
      jest.spyOn(require('@/integrations/supabase/client').supabase, 'from')
        .mockReturnValueOnce({
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          in: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({ data: { role: 'admin' }, error: null })
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isAdmin).toBe(true);
      });
    });

    it('should handle non-admin users', async () => {
      // Start with authenticated non-admin session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: 'tenant'
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValueOnce({ data: { session: mockSession }, error: null });

      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'onAuthStateChange')
        .mockImplementationOnce((callback) => {
          setTimeout(() => {
            callback('SIGNED_IN', mockSession);
          }, 0);
          return { unsubscribe: jest.fn() };
        });

      // Mock non-admin role check
      jest.spyOn(require('@/integrations/supabase/client').supabase, 'from')
        .mockReturnValueOnce({
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          in: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({ data: null, error: null })
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isAdmin).toBe(false);
      });
    });
  });
});
