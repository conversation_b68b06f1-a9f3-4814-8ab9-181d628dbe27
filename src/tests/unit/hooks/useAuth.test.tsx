// =====================================================
// AUTH HOOK UNIT TESTS
// Comprehensive tests for authentication hook
// =====================================================

import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuth, AuthProvider } from '@/hooks/useAuth';
import { mockUser, createTestQueryClient } from '@tests/setup/test-utils';
import { server } from '@tests/mocks/server';
import { rest } from 'msw';

// Test wrapper component
const createWrapper = (initialUser: any = null) => {
  const queryClient = createTestQueryClient();
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {children}
      </AuthProvider>
    </QueryClientProvider>
  );
};

describe('useAuth Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();
  });

  describe('Initial State', () => {
    it('should initialize with no user when not authenticated', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      expect(result.current.user).toBeNull();
      expect(result.current.loading).toBe(true);
    });

    it('should initialize with user when authenticated', async () => {
      // Mock the auth session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: mockUser.role
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValue({ data: { session: mockSession }, error: null });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.user).toBeTruthy();
        expect(result.current.user?.id).toBe(mockUser.id);
        expect(result.current.loading).toBe(false);
      });
    });
  });

  describe('Login Functionality', () => {
    it('should login successfully with valid credentials', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await act(async () => {
        await result.current.signIn('<EMAIL>', 'password123');
      });

      await waitFor(() => {
        expect(result.current.user).toBeTruthy();
        expect(result.current.loading).toBe(false);
      });
    });

    it('should handle login failure with invalid credentials', async () => {
      // Mock failed login response
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'signInWithPassword')
        .mockResolvedValue({ data: null, error: { message: 'Invalid credentials' } });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      let error;
      await act(async () => {
        const response = await result.current.signIn('<EMAIL>', 'wrongpassword');
        error = response.error;
      });

      await waitFor(() => {
        expect(result.current.user).toBeNull();
        expect(error).toBeTruthy();
        expect(error.message).toBe('Invalid credentials');
      });
    });

    it('should set loading state during login', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      let promise;
      act(() => {
        promise = result.current.signIn('<EMAIL>', 'password123');
      });

      expect(result.current.loading).toBe(true);

      await act(async () => {
        await promise;
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });
    });
  });

  describe('Registration Functionality', () => {
    it('should register successfully with valid data', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await act(async () => {
        await result.current.signUp('<EMAIL>', 'password123', 'New User');
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });
    });

    it('should handle registration failure', async () => {
      // Mock registration failure
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'signUp')
        .mockResolvedValue({ data: null, error: { message: 'Email already exists' } });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      let error;
      await act(async () => {
        const response = await result.current.signUp('<EMAIL>', 'password123', 'Existing User');
        error = response.error;
      });

      await waitFor(() => {
        expect(result.current.user).toBeNull();
        expect(error).toBeTruthy();
        expect(error.message).toBe('Email already exists');
      });
    });
  });

  describe('Logout Functionality', () => {
    it('should logout successfully', async () => {
      // Mock authenticated session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: mockUser.role
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValue({ data: { session: mockSession }, error: null });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.user).toBeTruthy();
      });

      await act(async () => {
        await result.current.signOut();
      });

      await waitFor(() => {
        expect(result.current.user).toBeNull();
      });
    });
  });

  describe('Admin Status', () => {
    it('should correctly identify admin users', async () => {
      // Mock authenticated admin session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: 'admin'
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValue({ data: { session: mockSession }, error: null });

      // Mock admin role check
      jest.spyOn(require('@/integrations/supabase/client').supabase, 'from')
        .mockReturnValue({
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          in: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({ data: { role: 'admin' }, error: null })
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isAdmin).toBe(true);
      });
    });

    it('should handle non-admin users', async () => {
      // Mock authenticated non-admin session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: 'tenant'
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValue({ data: { session: mockSession }, error: null });

      // Mock admin role check
      jest.spyOn(require('@/integrations/supabase/client').supabase, 'from')
        .mockReturnValue({
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          in: jest.fn().mockReturnThis(),
          single: jest.fn().mockRejectedValue(new Error('No admin role found'))
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isAdmin).toBe(false);
      });
    });
  });
});

  describe('Profile Update', () => {
    it('should update user profile successfully', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(mockUser)
      });

      const updateData = {
        full_name: 'Updated Name',
        phone: '+2348012345679'
      };

      await act(async () => {
        await result.current.updateProfile(updateData);
      });

      await waitFor(() => {
        expect(result.current.user?.full_name).toBe('Updated Name');
        expect(result.current.user?.phone).toBe('+2348012345679');
      });
    });

    it('should handle profile update failure', async () => {
      server.use(
        rest.patch('*/rest/v1/users/*', (req, res, ctx) => {
          return res(
            ctx.status(400),
            ctx.json({ error: 'Invalid phone number' })
          );
        })
      );

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(mockUser)
      });

      const updateData = {
        phone: 'invalid-phone'
      };

      await act(async () => {
        await result.current.updateProfile(updateData);
      });

      await waitFor(() => {
        expect(result.current.error).toBe('Invalid phone number');
      });
    });
  });

  describe('Password Reset', () => {
    it('should send password reset email successfully', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await act(async () => {
        await result.current.resetPassword('<EMAIL>');
      });

      // Should not throw error and complete successfully
      expect(result.current.error).toBeNull();
    });

    it('should handle password reset failure', async () => {
      server.use(
        rest.post('*/auth/v1/recover', (req, res, ctx) => {
          return res(
            ctx.status(400),
            ctx.json({ error: 'Email not found' })
          );
        })
      );

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await act(async () => {
        await result.current.resetPassword('<EMAIL>');
      });

      await waitFor(() => {
        expect(result.current.error).toBe('Email not found');
      });
    });
  });

  describe('Session Management', () => {
    it('should refresh session when token is valid', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      // Mock valid session
      localStorage.setItem('auth_token', 'valid-token');

      await act(async () => {
        await result.current.refreshSession();
      });

      await waitFor(() => {
        expect(result.current.user).toEqual(mockUser);
        expect(result.current.isAuthenticated).toBe(true);
      });
    });

    it('should handle invalid session token', async () => {
      server.use(
        rest.get('*/auth/v1/user', (req, res, ctx) => {
          return res(
            ctx.status(401),
            ctx.json({ error: 'Invalid token' })
          );
        })
      );

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      localStorage.setItem('auth_token', 'invalid-token');

      await act(async () => {
        await result.current.refreshSession();
      });

      await waitFor(() => {
        expect(result.current.user).toBeNull();
        expect(result.current.isAuthenticated).toBe(false);
        expect(localStorage.getItem('auth_token')).toBeNull();
      });
    });
  });

  describe('Error Handling', () => {
    it('should clear errors when calling clearError', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      // Simulate an error state
      act(() => {
        (result.current as any).setError('Test error');
      });

      expect(result.current.error).toBe('Test error');

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });

    it('should handle network errors gracefully', async () => {
      server.use(
        rest.post('*/auth/v1/token', (req, res, ctx) => {
          return res.networkError('Network error');
        })
      );

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await act(async () => {
        await result.current.login('<EMAIL>', 'password123');
      });

      await waitFor(() => {
        expect(result.current.error).toContain('Network error');
        expect(result.current.isAuthenticated).toBe(false);
      });
    });
  });

  describe('Role-based Access', () => {
    it('should correctly identify user roles', () => {
      const landlordUser = { ...mockUser, role: 'landlord' };
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(landlordUser)
      });

      expect(result.current.isLandlord).toBe(true);
      expect(result.current.isTenant).toBe(false);
      expect(result.current.isAdmin).toBe(false);
    });

    it('should correctly identify admin users', () => {
      const adminUser = { ...mockUser, role: 'admin' };
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(adminUser)
      });

      expect(result.current.isAdmin).toBe(true);
      expect(result.current.isLandlord).toBe(false);
      expect(result.current.isTenant).toBe(false);
    });
  });

  describe('Token Management', () => {
    it('should store auth token in localStorage on successful login', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await act(async () => {
        await result.current.login('<EMAIL>', 'password123');
      });

      await waitFor(() => {
        expect(localStorage.getItem('auth_token')).toBe('mock-access-token');
      });
    });

    it('should remove auth token from localStorage on logout', async () => {
      localStorage.setItem('auth_token', 'test-token');
      
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(mockUser)
      });

      await act(async () => {
        await result.current.logout();
      });

      await waitFor(() => {
        expect(localStorage.getItem('auth_token')).toBeNull();
      });
    });
  });
});
