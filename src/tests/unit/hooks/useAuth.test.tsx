// =====================================================
// AUTH HOOK UNIT TESTS
// Comprehensive tests for authentication hook
// =====================================================

import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuth, AuthProvider } from '@/hooks/useAuth';
import { mockUser, createTestQueryClient } from '@tests/setup/test-utils';
import { server } from '@tests/mocks/server';
import { rest } from 'msw';

// Test wrapper component
const createWrapper = (initialUser: any = null) => {
  const queryClient = createTestQueryClient();
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {children}
      </AuthProvider>
    </QueryClientProvider>
  );
};

describe('useAuth Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();
  });

  describe('Initial State', () => {
    beforeEach(() => {
      // Mock the auth state change subscription
      const mockUnsubscribe = jest.fn();
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'onAuthStateChange')
        .mockImplementation((callback) => {
          // Simulate initial auth state
          callback('INITIAL_SESSION', null);
          return {
            unsubscribe: mockUnsubscribe
          };
        });
    });

    it('should initialize with no user when not authenticated', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      expect(result.current.user).toBeNull();
      expect(result.current.loading).toBe(false); // Loading should be false after initial state
    });

    it('should initialize with user when authenticated', async () => {
      // Mock the auth session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: mockUser.role
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValue({ data: { session: mockSession }, error: null });

      // Mock auth state change to include the session
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'onAuthStateChange')
        .mockImplementation((callback) => {
          callback('SIGNED_IN', mockSession);
          return {
            unsubscribe: jest.fn()
          };
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.user).toBeTruthy();
        expect(result.current.user?.id).toBe(mockUser.id);
        expect(result.current.loading).toBe(false);
      });
    });
  });

  describe('Login Functionality', () => {
    it('should login successfully with valid credentials', async () => {
      const mockSession = {
        user: {
          id: mockUser.id,
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'Test User',
            role: 'user'
          }
        }
      };

      // Mock successful login
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'signInWithPassword')
        .mockResolvedValue({ data: { session: mockSession }, error: null });

      // Mock auth state change for successful login
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'onAuthStateChange')
        .mockImplementation((callback) => {
          callback('SIGNED_IN', mockSession);
          return {
            unsubscribe: jest.fn()
          };
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await act(async () => {
        const response = await result.current.signIn('<EMAIL>', 'password123');
        expect(response.error).toBeUndefined();
      });

      await waitFor(() => {
        expect(result.current.user).toBeTruthy();
        expect(result.current.user?.email).toBe('<EMAIL>');
        expect(result.current.loading).toBe(false);
      });
    });

    it('should handle login failure with invalid credentials', async () => {
      // Mock failed login response
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'signInWithPassword')
        .mockResolvedValue({ data: null, error: { message: 'Invalid credentials' } });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      let error;
      await act(async () => {
        const response = await result.current.signIn('<EMAIL>', 'wrongpassword');
        error = response.error;
      });

      await waitFor(() => {
        expect(result.current.user).toBeNull();
        expect(error).toBeTruthy();
        expect(error.message).toBe('Invalid credentials');
      });
    });

    it('should set loading state during login', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      let promise;
      act(() => {
        promise = result.current.signIn('<EMAIL>', 'password123');
      });

      expect(result.current.loading).toBe(true);

      await act(async () => {
        await promise;
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });
    });
  });

  describe('Registration Functionality', () => {
    it('should register successfully with valid data', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await act(async () => {
        await result.current.signUp('<EMAIL>', 'password123', 'New User');
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });
    });

    it('should handle registration failure', async () => {
      // Mock registration failure
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'signUp')
        .mockResolvedValue({ data: null, error: { message: 'Email already exists' } });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      let error;
      await act(async () => {
        const response = await result.current.signUp('<EMAIL>', 'password123', 'Existing User');
        error = response.error;
      });

      await waitFor(() => {
        expect(result.current.user).toBeNull();
        expect(error).toBeTruthy();
        expect(error.message).toBe('Email already exists');
      });
    });
  });

  describe('Logout Functionality', () => {
    it('should logout successfully', async () => {
      // Mock authenticated session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: mockUser.role
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValue({ data: { session: mockSession }, error: null });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.user).toBeTruthy();
      });

      await act(async () => {
        await result.current.signOut();
      });

      await waitFor(() => {
        expect(result.current.user).toBeNull();
      });
    });
  });

  describe('Admin Status', () => {
    it('should correctly identify admin users', async () => {
      // Mock authenticated admin session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: 'admin'
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValue({ data: { session: mockSession }, error: null });

      // Mock admin role check
      jest.spyOn(require('@/integrations/supabase/client').supabase, 'from')
        .mockReturnValue({
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          in: jest.fn().mockReturnThis(),
          single: jest.fn().mockResolvedValue({ data: { role: 'admin' }, error: null })
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isAdmin).toBe(true);
      });
    });

    it('should handle non-admin users', async () => {
      // Mock authenticated non-admin session
      const mockSession = {
        user: {
          id: mockUser.id,
          email: mockUser.email,
          user_metadata: {
            full_name: mockUser.full_name,
            role: 'tenant'
          }
        }
      };
      
      jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
        .mockResolvedValue({ data: { session: mockSession }, error: null });

      // Mock admin role check
      jest.spyOn(require('@/integrations/supabase/client').supabase, 'from')
        .mockReturnValue({
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          in: jest.fn().mockReturnThis(),
          single: jest.fn().mockRejectedValue(new Error('No admin role found'))
        });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isAdmin).toBe(false);
      });
    });
  });
});
