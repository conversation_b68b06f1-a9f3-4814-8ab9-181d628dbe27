import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateLoginAttemptsTable1703003000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'login_attempts',
        columns: [
          // ... existing columns
        ],
      })
    );

    // Create indices
    await queryRunner.createIndex(
      'login_attempts',
      new TableIndex({
        name: 'IDX_login_attempts_email',
        columnNames: ['email'],
      })
    );

    await queryRunner.createIndex(
      'login_attempts',
      new TableIndex({
        name: 'IDX_login_attempts_ipAddress',
        columnNames: ['ipAddress'],
      })
    );

    await queryRunner.createIndex(
      'login_attempts',
      new TableIndex({
        name: 'IDX_login_attempts_email_ipAddress',
        columnNames: ['email', 'ipAddress'],
      })
    );

    await queryRunner.createIndex(
      'login_attempts',
      new TableIndex({
        name: 'IDX_login_attempts_createdAt',
        columnNames: ['createdAt'],
      })
    );

    await queryRunner.createIndex(
      'login_attempts',
      new TableIndex({
        name: 'IDX_login_attempts_success',
        columnNames: ['success'],
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('login_attempts', 'IDX_login_attempts_success');
    await queryRunner.dropIndex('login_attempts', 'IDX_login_attempts_createdAt');
    await queryRunner.dropIndex('login_attempts', 'IDX_login_attempts_email_ipAddress');
    await queryRunner.dropIndex('login_attempts', 'IDX_login_attempts_ipAddress');
    await queryRunner.dropIndex('login_attempts', 'IDX_login_attempts_email');
    await queryRunner.dropTable('login_attempts');
  }
} 