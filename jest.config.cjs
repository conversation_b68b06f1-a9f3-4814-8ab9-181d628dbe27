// =====================================================
// JEST CONFIGURATION
// Comprehensive testing configuration for PHCityRent
// =====================================================

/** @type {import('jest').Config} */
module.exports = {
  // Root directory
  rootDir: '.',

  // Test environment
  testEnvironment: 'jsdom',
  
  // Setup files
  setupFiles: [
    '<rootDir>/src/tests/setup/polyfills.ts'
  ],
  
  // Setup files after env
  setupFilesAfterEnv: [
    '<rootDir>/src/tests/setup/jest.setup.ts',
    '<rootDir>/src/tests/setup/test-utils.tsx'
  ],
  
  // Module name mapper
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/src/tests/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/src/tests/__mocks__/fileMock.js'
  },

  // Transform configuration
  transform: {
    '^.+\\.(t|j)sx?$': ['@swc/jest', {
      jsc: {
        parser: {
          syntax: 'typescript',
          tsx: true,
          decorators: true
        },
        transform: {
          react: {
            runtime: 'automatic'
          }
        }
      }
    }]
  },

  // Test match patterns
  testMatch: [
    '<rootDir>/src/tests/**/*.test.{ts,tsx}'
  ],

  // Coverage configuration
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/tests/**/*',
    '!src/**/*.d.ts'
  ],

  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Test environment configuration
  testEnvironmentOptions: {
    url: 'http://localhost'
  },

  // Global configuration
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json'
    }
  },

  // Ignore patterns
  transformIgnorePatterns: [
    '/node_modules/(?!(@supabase|@tanstack|@radix-ui)/)'
  ]
}; 