-- =====================================================
-- PRODUCTION-GRADE DATABASE OPTIMIZATION
-- Enterprise-level database performance, indexing, and monitoring
-- =====================================================

-- Enable required extensions for advanced performance
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS btree_gin;
CREATE EXTENSION IF NOT EXISTS btree_gist;
CREATE EXTENSION IF NOT EXISTS pg_buffercache;
CREATE EXTENSION IF NOT EXISTS pgstattuple;
CREATE EXTENSION IF NOT EXISTS pg_prewarm;
CREATE EXTENSION IF NOT EXISTS hypopg; -- Hypothetical indexes for testing

-- =====================================================
-- OPTIMIZED INDEXES FOR SEARCH QUERIES
-- =====================================================

-- Properties table indexes for high-performance search
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_location_trgm 
ON public.properties USING gin (location gin_trgm_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_title_trgm 
ON public.properties USING gin (title gin_trgm_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_description_trgm 
ON public.properties USING gin (description gin_trgm_ops);

-- Composite indexes for common search patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_search_composite 
ON public.properties (location, property_type, is_available, price_per_month);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_price_range 
ON public.properties (price_per_month, bedrooms, bathrooms) 
WHERE is_available = true;

-- GIN index for amenities array search
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_amenities_gin 
ON public.properties USING gin (amenities);

-- Spatial index for location-based queries (if using PostGIS)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_location_spatial 
ON public.properties USING gist (location) 
WHERE location IS NOT NULL;

-- Agent applications indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_applications_status_created 
ON public.agent_applications (status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_applications_location_status 
ON public.agent_applications (operating_areas, status) 
USING gin;

-- Payment transactions indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_transactions_user_status 
ON public.payment_transactions (user_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_transactions_property_date 
ON public.payment_transactions (property_id, created_at DESC);

-- Property inquiries indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_property_inquiries_agent_status 
ON public.property_inquiries (agent_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_property_inquiries_property_date 
ON public.property_inquiries (property_id, created_at DESC);

-- User behavior tracking indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_behavior_user_activity 
ON public.user_behavior (user_id, activity_type, created_at DESC);

-- AI recommendations indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_recommendations_user_score 
ON public.ai_recommendations (user_id, confidence_score DESC, created_at DESC);

-- =====================================================
-- DATABASE PARTITIONING FOR LARGE TABLES
-- =====================================================

-- Partition payment_transactions by month for better performance
CREATE TABLE IF NOT EXISTS public.payment_transactions_partitioned (
    LIKE public.payment_transactions INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- Create monthly partitions for the current year and next year
DO $$
DECLARE
    start_date DATE;
    end_date DATE;
    partition_name TEXT;
BEGIN
    -- Create partitions for current year
    FOR i IN 0..23 LOOP
        start_date := DATE_TRUNC('month', CURRENT_DATE) + (i || ' months')::INTERVAL;
        end_date := start_date + INTERVAL '1 month';
        partition_name := 'payment_transactions_' || TO_CHAR(start_date, 'YYYY_MM');
        
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS public.%I 
            PARTITION OF public.payment_transactions_partitioned
            FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );
    END LOOP;
END $$;

-- Partition user_behavior by month
CREATE TABLE IF NOT EXISTS public.user_behavior_partitioned (
    LIKE public.user_behavior INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- Create monthly partitions for user behavior
DO $$
DECLARE
    start_date DATE;
    end_date DATE;
    partition_name TEXT;
BEGIN
    FOR i IN 0..23 LOOP
        start_date := DATE_TRUNC('month', CURRENT_DATE) + (i || ' months')::INTERVAL;
        end_date := start_date + INTERVAL '1 month';
        partition_name := 'user_behavior_' || TO_CHAR(start_date, 'YYYY_MM');
        
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS public.%I 
            PARTITION OF public.user_behavior_partitioned
            FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );
    END LOOP;
END $$;

-- =====================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- =====================================================

-- Property search materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS public.mv_property_search AS
SELECT 
    p.id,
    p.title,
    p.location,
    p.price_per_month,
    p.bedrooms,
    p.bathrooms,
    p.property_type,
    p.amenities,
    p.is_available,
    p.created_at,
    p.updated_at,
    COALESCE(pi.inquiry_count, 0) as inquiry_count,
    COALESCE(pv.view_count, 0) as view_count,
    COALESCE(pr.avg_rating, 0) as avg_rating
FROM public.properties p
LEFT JOIN (
    SELECT property_id, COUNT(*) as inquiry_count
    FROM public.property_inquiries
    GROUP BY property_id
) pi ON p.id = pi.property_id
LEFT JOIN (
    SELECT property_id, COUNT(*) as view_count
    FROM public.user_behavior
    WHERE activity_type = 'property_view'
    GROUP BY property_id
) pv ON p.id = pv.property_id
LEFT JOIN (
    SELECT property_id, AVG(rating::NUMERIC) as avg_rating
    FROM public.property_interactions
    WHERE rating IS NOT NULL
    GROUP BY property_id
) pr ON p.id = pr.property_id
WHERE p.is_available = true;

-- Create indexes on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_property_search_id 
ON public.mv_property_search (id);

CREATE INDEX IF NOT EXISTS idx_mv_property_search_location 
ON public.mv_property_search USING gin (location gin_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_mv_property_search_price 
ON public.mv_property_search (price_per_month, bedrooms, bathrooms);

-- Agent performance materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS public.mv_agent_performance AS
SELECT 
    aa.agent_id,
    aa.full_name,
    aa.operating_areas,
    COUNT(DISTINCT p.id) as total_properties,
    COUNT(DISTINCT pi.id) as total_inquiries,
    COUNT(DISTINCT pt.id) as total_transactions,
    COALESCE(SUM(pt.amount), 0) as total_revenue,
    COALESCE(AVG(pi.response_time_hours), 0) as avg_response_time,
    COALESCE(AVG(pt.rating), 0) as avg_rating,
    MAX(pi.created_at) as last_activity
FROM public.agent_applications aa
LEFT JOIN public.properties p ON aa.agent_id = p.agent_id
LEFT JOIN public.property_inquiries pi ON aa.agent_id = pi.agent_id
LEFT JOIN public.payment_transactions pt ON p.id = pt.property_id
WHERE aa.status = 'approved'
GROUP BY aa.agent_id, aa.full_name, aa.operating_areas;

-- Create indexes on agent performance view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_agent_performance_agent_id 
ON public.mv_agent_performance (agent_id);

CREATE INDEX IF NOT EXISTS idx_mv_agent_performance_revenue 
ON public.mv_agent_performance (total_revenue DESC);

-- =====================================================
-- STORED PROCEDURES FOR COMPLEX OPERATIONS
-- =====================================================

-- Procedure to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_performance_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.mv_property_search;
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.mv_agent_performance;
    
    -- Log the refresh
    INSERT INTO public.system_activity_log (
        activity_type, details
    ) VALUES (
        'materialized_view_refresh',
        json_build_object(
            'views_refreshed', ARRAY['mv_property_search', 'mv_agent_performance'],
            'timestamp', NOW()
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Procedure for intelligent property search with ranking
CREATE OR REPLACE FUNCTION search_properties_ranked(
    p_search_text TEXT DEFAULT NULL,
    p_location TEXT DEFAULT NULL,
    p_min_price INTEGER DEFAULT NULL,
    p_max_price INTEGER DEFAULT NULL,
    p_bedrooms INTEGER DEFAULT NULL,
    p_bathrooms INTEGER DEFAULT NULL,
    p_property_type TEXT DEFAULT NULL,
    p_amenities TEXT[] DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE(
    id UUID,
    title TEXT,
    location TEXT,
    price_per_month INTEGER,
    bedrooms INTEGER,
    bathrooms INTEGER,
    property_type TEXT,
    amenities TEXT[],
    inquiry_count BIGINT,
    view_count BIGINT,
    avg_rating NUMERIC,
    search_rank REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mvps.id,
        mvps.title,
        mvps.location,
        mvps.price_per_month,
        mvps.bedrooms,
        mvps.bathrooms,
        mvps.property_type,
        mvps.amenities,
        mvps.inquiry_count,
        mvps.view_count,
        mvps.avg_rating,
        (
            COALESCE(similarity(mvps.title, COALESCE(p_search_text, '')), 0) * 0.4 +
            COALESCE(similarity(mvps.location, COALESCE(p_location, mvps.location)), 0) * 0.3 +
            (mvps.view_count::REAL / GREATEST(MAX(mvps.view_count) OVER (), 1)) * 0.2 +
            (mvps.avg_rating / 5.0) * 0.1
        ) as search_rank
    FROM public.mv_property_search mvps
    WHERE 
        (p_search_text IS NULL OR (
            mvps.title ILIKE '%' || p_search_text || '%' OR
            mvps.location ILIKE '%' || p_search_text || '%'
        ))
        AND (p_location IS NULL OR mvps.location ILIKE '%' || p_location || '%')
        AND (p_min_price IS NULL OR mvps.price_per_month >= p_min_price)
        AND (p_max_price IS NULL OR mvps.price_per_month <= p_max_price)
        AND (p_bedrooms IS NULL OR mvps.bedrooms = p_bedrooms)
        AND (p_bathrooms IS NULL OR mvps.bathrooms = p_bathrooms)
        AND (p_property_type IS NULL OR mvps.property_type = p_property_type)
        AND (p_amenities IS NULL OR mvps.amenities && p_amenities)
    ORDER BY search_rank DESC, mvps.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql STABLE;

-- Procedure for batch property updates with optimistic locking
CREATE OR REPLACE FUNCTION batch_update_properties(
    p_property_updates JSONB
)
RETURNS TABLE(
    property_id UUID,
    success BOOLEAN,
    error_message TEXT
) AS $$
DECLARE
    update_record JSONB;
    current_version INTEGER;
    expected_version INTEGER;
BEGIN
    FOR update_record IN SELECT * FROM jsonb_array_elements(p_property_updates)
    LOOP
        BEGIN
            -- Get current version for optimistic locking
            SELECT version INTO current_version
            FROM public.properties
            WHERE id = (update_record->>'id')::UUID;

            expected_version := (update_record->>'version')::INTEGER;

            IF current_version != expected_version THEN
                RETURN QUERY SELECT
                    (update_record->>'id')::UUID,
                    false,
                    'Version mismatch - property was modified by another user';
                CONTINUE;
            END IF;

            -- Perform the update
            UPDATE public.properties SET
                title = COALESCE(update_record->>'title', title),
                description = COALESCE(update_record->>'description', description),
                price_per_year = COALESCE((update_record->>'price_per_year')::INTEGER, price_per_year),
                location = COALESCE(update_record->>'location', location),
                bedrooms = COALESCE((update_record->>'bedrooms')::INTEGER, bedrooms),
                bathrooms = COALESCE((update_record->>'bathrooms')::INTEGER, bathrooms),
                property_type = COALESCE(update_record->>'property_type', property_type),
                amenities = COALESCE(
                    ARRAY(SELECT jsonb_array_elements_text(update_record->'amenities')),
                    amenities
                ),
                updated_at = NOW(),
                version = version + 1
            WHERE id = (update_record->>'id')::UUID;

            RETURN QUERY SELECT
                (update_record->>'id')::UUID,
                true,
                'Success'::TEXT;

        EXCEPTION WHEN OTHERS THEN
            RETURN QUERY SELECT
                (update_record->>'id')::UUID,
                false,
                SQLERRM;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- DATABASE BACKUP AND RECOVERY PROCEDURES
-- =====================================================

-- Table to track backup operations
CREATE TABLE IF NOT EXISTS public.backup_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    backup_type TEXT NOT NULL CHECK (backup_type IN ('full', 'incremental', 'differential')),
    status TEXT NOT NULL CHECK (status IN ('started', 'in_progress', 'completed', 'failed')),
    backup_size_bytes BIGINT,
    backup_location TEXT,
    tables_included TEXT[],
    start_time TIMESTAMPTZ DEFAULT NOW(),
    end_time TIMESTAMPTZ,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'
);

-- Function to initiate database backup
CREATE OR REPLACE FUNCTION initiate_backup(
    p_backup_type TEXT DEFAULT 'incremental',
    p_tables TEXT[] DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    backup_id UUID;
    table_list TEXT[];
BEGIN
    -- Generate backup ID
    backup_id := gen_random_uuid();

    -- Determine tables to backup
    IF p_tables IS NULL THEN
        SELECT ARRAY_AGG(tablename) INTO table_list
        FROM pg_tables
        WHERE schemaname = 'public'
        AND tablename NOT LIKE '%_audit%'
        AND tablename NOT LIKE '%_log%';
    ELSE
        table_list := p_tables;
    END IF;

    -- Record backup operation
    INSERT INTO public.backup_operations (
        id, backup_type, status, tables_included, metadata
    ) VALUES (
        backup_id, p_backup_type, 'started', table_list,
        json_build_object(
            'initiated_by', current_user,
            'estimated_size', pg_database_size(current_database())
        )
    );

    -- Log the backup initiation
    INSERT INTO public.system_activity_log (
        activity_type, details
    ) VALUES (
        'backup_initiated',
        json_build_object(
            'backup_id', backup_id,
            'backup_type', p_backup_type,
            'tables_count', array_length(table_list, 1)
        )
    );

    RETURN backup_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- DATABASE MONITORING AND PERFORMANCE TRACKING
-- =====================================================

-- Real-time database metrics view
CREATE OR REPLACE VIEW public.v_database_metrics AS
SELECT
    -- Connection metrics
    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
    (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections,
    (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle in transaction') as idle_in_transaction,

    -- Database size metrics
    pg_size_pretty(pg_database_size(current_database())) as database_size,
    pg_database_size(current_database()) as database_size_bytes,

    -- Cache hit ratios
    ROUND(
        (sum(heap_blks_hit) / GREATEST(sum(heap_blks_hit + heap_blks_read), 1)) * 100, 2
    ) as cache_hit_ratio,

    -- Transaction metrics
    (SELECT sum(xact_commit) FROM pg_stat_database WHERE datname = current_database()) as total_commits,
    (SELECT sum(xact_rollback) FROM pg_stat_database WHERE datname = current_database()) as total_rollbacks,

    -- Current timestamp
    NOW() as measured_at
FROM pg_stat_user_tables;

-- Function to collect performance metrics
CREATE OR REPLACE FUNCTION collect_performance_metrics()
RETURNS void AS $$
DECLARE
    metrics_record RECORD;
BEGIN
    -- Collect current metrics
    SELECT * INTO metrics_record FROM public.v_database_metrics;

    -- Store in performance history
    INSERT INTO public.query_performance_logs (
        query_hash, query_text, execution_time_ms,
        rows_examined, rows_returned, endpoint
    )
    SELECT
        md5(query), query, mean_time::INTEGER,
        rows::INTEGER, calls::INTEGER, 'system_metrics'
    FROM pg_stat_statements
    WHERE mean_time > 100 -- Only log slow queries
    AND calls > 10 -- Only frequently executed queries
    ORDER BY mean_time DESC
    LIMIT 100;

    -- Clean up old performance data (keep last 30 days)
    DELETE FROM public.query_performance_logs
    WHERE timestamp < NOW() - INTERVAL '30 days';

    -- Log the collection
    INSERT INTO public.system_activity_log (
        activity_type, details
    ) VALUES (
        'performance_metrics_collected',
        json_build_object(
            'active_connections', metrics_record.active_connections,
            'cache_hit_ratio', metrics_record.cache_hit_ratio,
            'database_size_mb', metrics_record.database_size_bytes / 1024 / 1024
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- READ REPLICA CONFIGURATION HELPERS
-- =====================================================

-- Function to check if we're on a read replica
CREATE OR REPLACE FUNCTION is_read_replica()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN pg_is_in_recovery();
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to route queries to appropriate server
CREATE OR REPLACE FUNCTION get_connection_info()
RETURNS TABLE(
    server_type TEXT,
    is_primary BOOLEAN,
    lag_seconds INTEGER
) AS $$
BEGIN
    IF pg_is_in_recovery() THEN
        RETURN QUERY SELECT
            'read_replica'::TEXT,
            false,
            EXTRACT(EPOCH FROM (NOW() - pg_last_xact_replay_timestamp()))::INTEGER;
    ELSE
        RETURN QUERY SELECT
            'primary'::TEXT,
            true,
            0;
    END IF;
END;
$$ LANGUAGE plpgsql STABLE;

-- =====================================================
-- AUTOMATED MAINTENANCE PROCEDURES
-- =====================================================

-- Comprehensive database maintenance function
CREATE OR REPLACE FUNCTION perform_database_maintenance()
RETURNS void AS $$
DECLARE
    maintenance_start TIMESTAMPTZ;
    table_record RECORD;
    index_record RECORD;
BEGIN
    maintenance_start := NOW();

    -- Only run on primary server
    IF pg_is_in_recovery() THEN
        RAISE NOTICE 'Skipping maintenance on read replica';
        RETURN;
    END IF;

    -- Update table statistics
    FOR table_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE schemaname = 'public'
    LOOP
        EXECUTE format('ANALYZE %I.%I', table_record.schemaname, table_record.tablename);
    END LOOP;

    -- Reindex fragmented indexes
    FOR index_record IN
        SELECT schemaname, indexname, tablename
        FROM pg_indexes
        WHERE schemaname = 'public'
        AND indexname NOT LIKE 'pg_%'
    LOOP
        -- Check if index needs reindexing (simplified check)
        BEGIN
            EXECUTE format('REINDEX INDEX CONCURRENTLY %I.%I',
                index_record.schemaname, index_record.indexname);
        EXCEPTION WHEN OTHERS THEN
            -- Log the error but continue
            INSERT INTO public.system_activity_log (
                activity_type, details
            ) VALUES (
                'maintenance_error',
                json_build_object(
                    'error', SQLERRM,
                    'index', index_record.indexname,
                    'table', index_record.tablename
                )
            );
        END;
    END LOOP;

    -- Refresh materialized views
    PERFORM refresh_performance_views();

    -- Clean up old audit logs (keep 90 days)
    DELETE FROM public.property_audit_log WHERE created_at < NOW() - INTERVAL '90 days';
    DELETE FROM public.payment_audit_log WHERE created_at < NOW() - INTERVAL '90 days';
    DELETE FROM public.system_activity_log WHERE created_at < NOW() - INTERVAL '90 days';

    -- Vacuum analyze critical tables
    VACUUM ANALYZE public.properties;
    VACUUM ANALYZE public.payment_transactions;
    VACUUM ANALYZE public.property_inquiries;
    VACUUM ANALYZE public.agent_applications;

    -- Log maintenance completion
    INSERT INTO public.system_activity_log (
        activity_type, details
    ) VALUES (
        'database_maintenance_completed',
        json_build_object(
            'duration_minutes', EXTRACT(EPOCH FROM (NOW() - maintenance_start)) / 60,
            'tables_analyzed', (
                SELECT count(*) FROM pg_tables WHERE schemaname = 'public'
            ),
            'maintenance_type', 'automated'
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- QUERY OPTIMIZATION HELPERS
-- =====================================================

-- Function to analyze query performance
CREATE OR REPLACE FUNCTION analyze_query_performance(
    p_query_text TEXT,
    p_explain_analyze BOOLEAN DEFAULT false
)
RETURNS TABLE(
    execution_plan TEXT,
    estimated_cost NUMERIC,
    actual_time NUMERIC,
    recommendations TEXT[]
) AS $$
DECLARE
    plan_result TEXT;
    recommendations TEXT[] := '{}';
BEGIN
    -- Get query execution plan
    IF p_explain_analyze THEN
        EXECUTE 'EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) ' || p_query_text INTO plan_result;
    ELSE
        EXECUTE 'EXPLAIN (FORMAT TEXT) ' || p_query_text INTO plan_result;
    END IF;

    -- Analyze plan and generate recommendations
    IF plan_result ILIKE '%Seq Scan%' THEN
        recommendations := array_append(recommendations, 'Consider adding indexes for sequential scans');
    END IF;

    IF plan_result ILIKE '%Sort%' AND plan_result ILIKE '%external%' THEN
        recommendations := array_append(recommendations, 'Increase work_mem for external sorts');
    END IF;

    IF plan_result ILIKE '%Hash%' AND plan_result ILIKE '%Batches%' THEN
        recommendations := array_append(recommendations, 'Consider increasing hash_mem_multiplier');
    END IF;

    RETURN QUERY SELECT
        plan_result,
        0::NUMERIC, -- Placeholder for estimated cost
        0::NUMERIC, -- Placeholder for actual time
        recommendations;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- AUTOMATED SCHEDULING
-- =====================================================

-- Create pg_cron jobs for automated maintenance (if pg_cron is available)
DO $$
BEGIN
    -- Try to schedule maintenance job (will fail gracefully if pg_cron not available)
    BEGIN
        -- Daily maintenance at 2 AM
        PERFORM cron.schedule('daily-maintenance', '0 2 * * *', 'SELECT perform_database_maintenance();');

        -- Hourly performance metrics collection
        PERFORM cron.schedule('performance-metrics', '0 * * * *', 'SELECT collect_performance_metrics();');

        -- Weekly materialized view refresh
        PERFORM cron.schedule('weekly-mv-refresh', '0 3 * * 0', 'SELECT refresh_performance_views();');

    EXCEPTION WHEN OTHERS THEN
        -- Log that automated scheduling is not available
        INSERT INTO public.system_activity_log (
            activity_type, details
        ) VALUES (
            'scheduling_setup',
            json_build_object(
                'status', 'pg_cron_not_available',
                'message', 'Automated scheduling requires pg_cron extension'
            )
        );
    END;
END $$;

-- =====================================================
-- GRANTS AND PERMISSIONS
-- =====================================================

-- Grant necessary permissions for performance monitoring
GRANT SELECT ON public.v_database_metrics TO authenticated;
GRANT EXECUTE ON FUNCTION collect_performance_metrics() TO authenticated;
GRANT EXECUTE ON FUNCTION refresh_performance_views() TO authenticated;
GRANT EXECUTE ON FUNCTION search_properties_ranked(TEXT, TEXT, INTEGER, INTEGER, INTEGER, INTEGER, TEXT, TEXT[], INTEGER, INTEGER) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_connection_info() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION is_read_replica() TO authenticated, anon;

-- Restrict maintenance functions to admins only
GRANT EXECUTE ON FUNCTION perform_database_maintenance() TO authenticated;
GRANT EXECUTE ON FUNCTION initiate_backup(TEXT, TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION analyze_query_performance(TEXT, BOOLEAN) TO authenticated;

-- =====================================================
-- FINAL OPTIMIZATIONS
-- =====================================================

-- Set optimal PostgreSQL configuration parameters
-- Note: These should be set in postgresql.conf for production
/*
Recommended postgresql.conf settings for production:

# Memory settings
shared_buffers = 256MB                    # 25% of RAM
effective_cache_size = 1GB               # 75% of RAM
work_mem = 4MB                           # Per connection
maintenance_work_mem = 64MB              # For maintenance operations

# Checkpoint settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Query planner settings
random_page_cost = 1.1                   # For SSD storage
effective_io_concurrency = 200           # For SSD storage

# Connection settings
max_connections = 200
shared_preload_libraries = 'pg_stat_statements,pg_prewarm'

# Logging settings
log_min_duration_statement = 1000        # Log slow queries
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
*/

-- Log successful completion
INSERT INTO public.system_activity_log (
    activity_type, details
) VALUES (
    'database_optimization_completed',
    json_build_object(
        'migration', '20250710000001_production_database_optimization',
        'features_enabled', ARRAY[
            'advanced_indexing',
            'table_partitioning',
            'materialized_views',
            'stored_procedures',
            'backup_procedures',
            'performance_monitoring',
            'automated_maintenance'
        ],
        'timestamp', NOW()
    )
);
